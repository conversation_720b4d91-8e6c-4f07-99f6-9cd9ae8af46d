import React, { useMemo } from 'react';
import { Select } from "@snap/design-system";
import { CombinedReportFilterOption } from "../../global"
import { maskDocumentNumber } from '../../helpers';

interface CombinedReportFilterProps {
  reportSourcesUsed: Array<CombinedReportFilterOption>;
  selectedFilterId: string;
  onFilterChange: (filterId: string) => void;
  className?: string;
}

const CombinedReportFilter: React.FC<CombinedReportFilterProps> = ({
  reportSourcesUsed,
  selectedFilterId,
  onFilterChange,
  className = ""
}) => {
  const filterOptions = useMemo(() => {
    const allReportsOption = {
      value: 'combined_data',
      label: 'TODOS OS RELATÓRIOS JUNTOS'
    };

    const sourceOptions = reportSourcesUsed.map(source => ({
      value: source.user_reports_id,
      label: `${source.report_type} ${maskDocumentNumber(source.report_input_value, source.report_type)} - ${source.report_name}`.toUpperCase()
    }));

    return [allReportsOption, ...sourceOptions];
  }, [reportSourcesUsed]);

  return (
    <div className={`w-full mb-4 ${className}`}>
      <Select
        options={filterOptions}
        value={selectedFilterId}
        onChange={onFilterChange}
        placeholder="Selecionar filtro..."
        className="w-full"
      />
    </div>
  );
};

export default CombinedReportFilter;