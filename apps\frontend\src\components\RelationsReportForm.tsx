import { Select, Text } from "@snap/design-system";
import { ReportInput } from "~/components/ReportInput";
import { Checkbox } from "~/components/ui/checkbox";
import { Label } from "~/components/ui/label";

interface DocumentTypeOption {
  value: string;
  label: string;
}

interface RelationsReportFormProps {
  documentTypeOptions: DocumentTypeOption[];
  document1Type: string;
  document1Value: string;
  document2Type: string;
  document2Value: string;
  ignoreOtherContacts: boolean;
  ignoreConsortiums: boolean;
  selectedReportType: string;
  onDocument1TypeChange: (type: string) => void;
  onDocument1ValueChange: (value: string) => void;
  onDocument2TypeChange: (type: string) => void;
  onDocument2ValueChange: (value: string) => void;
  onIgnoreOtherContactsChange: (ignore: boolean) => void;
  onIgnoreConsortiumsChange: (ignore: boolean) => void;
  onKeyDown: (event: React.KeyboardEvent) => void;
}

export function RelationsReportForm({
  documentTypeOptions,
  document1Type,
  document1Value,
  document2Type,
  document2Value,
  ignoreOtherContacts,
  ignoreConsortiums,
  selectedReportType,
  onDocument1TypeChange,
  onDocument1ValueChange,
  onDocument2TypeChange,
  onDocument2ValueChange,
  onIgnoreOtherContactsChange,
  onIgnoreConsortiumsChange,
  onKeyDown
}: RelationsReportFormProps) {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Text className="">Tipo do documento:</Text>
          <Select
            options={documentTypeOptions}
            value={document1Type}
            onChange={onDocument1TypeChange}
            placeholder="Selecionar tipo"
            data-testid="select-document1-type"
            className="w-full"
          />
        </div>
        <div className="flex-1 pt-1.5">
          <Text className="">Preencha o campo:</Text>
          <ReportInput
            inputValue={document1Value}
            setInputValue={onDocument1ValueChange}
            reportType={selectedReportType}
            documentType={document1Type}
            onKeyDown={onKeyDown}
          />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex-1">
          <Text className="">Tipo do documento:</Text>
          <Select
            options={documentTypeOptions}
            value={document2Type}
            onChange={onDocument2TypeChange}
            placeholder="Selecionar tipo"
            data-testid="select-document2-type"
            className="w-full"
          />
        </div>
        <div className="flex-1 pt-1.5">
          <Text className="">Preencha o campo:</Text>
          <ReportInput
            inputValue={document2Value}
            setInputValue={onDocument2ValueChange}
            reportType={selectedReportType}
            documentType={document2Type}
            onKeyDown={onKeyDown}
          />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Checkbox
            id="ignorar-outros-contatos"
            checked={ignoreOtherContacts}
            onCheckedChange={onIgnoreOtherContactsChange}
            className="border-foreground data-[state=checked]:bg-foreground rounded-none cursor-pointer"
            title="Ignorar outros contatos"
          />
          <Label htmlFor="ignorar-outros-contatos" className="cursor-pointer">
            Ignorar outros contatos
          </Label>
        </div>
        <div className="flex items-center gap-2">
          <Checkbox
            id="ignorar-consorcios"
            checked={ignoreConsortiums}
            onCheckedChange={onIgnoreConsortiumsChange}
            className="border-foreground data-[state=checked]:bg-foreground rounded-none cursor-pointer"
            title="Ignorar consórcios"
          />
          <Label htmlFor="ignorar-consorcios" className="cursor-pointer">
            Ignorar consórcios
          </Label>
        </div>
      </div>
    </div>
  );
}
