import { Select, Text } from "@snap/design-system";
import { ReportInput } from "~/components/ReportInput";

interface ReportTypeOption {
  value: string;
  label: string;
}

interface IndividualReportFormProps {
  reportTypeOptions: ReportTypeOption[];
  selectedReportType: string;
  reportInputValue: string;
  onReportTypeChange: (type: string) => void;
  onInputValueChange: (value: string) => void;
  onKeyDown: (event: React.KeyboardEvent) => void;
  hasReportTypes: boolean;
}

export function IndividualReportForm({
  reportTypeOptions,
  selectedReportType,
  reportInputValue,
  onReportTypeChange,
  onInputValueChange,
  onKeyDown,
  hasReportTypes
}: IndividualReportFormProps) {
  return (
    <div className="grid grid-cols-2 gap-4">
      <div>
        <Text className="block mb-1">Tipo de entrada:</Text>
        {hasReportTypes ? (
          <Select
            options={reportTypeOptions}
            value={selectedReportType}
            onChange={onReportTypeChange}
            placeholder="Selecionar tipo"
            data-testid="select-report-type"
            className="w-full"
          />
        ) : (
          <p className="text-sm text-accent">Nenhum tipo de relatório disponível</p>
        )}
      </div>
      <div>
        <Text className="block mb-1">Preencha o campo:</Text>
        <div className="pt-1.5">
          <ReportInput
            inputValue={reportInputValue}
            setInputValue={onInputValueChange}
            reportType={selectedReportType}
            onKeyDown={onKeyDown}
          />
        </div>
      </div>
    </div>
  );
}
