services:
  backend:
    image: my-backend-image
    environment:
      K<PERSON><PERSON><PERSON><PERSON>K_URL: ${K<PERSON><PERSON><PERSON>OAK_URL}
      KE<PERSON><PERSON>OAK_ISSUER_URL: ${KEYCLOAK_ISSUER_URL}
      CLIENT_ID_KEYCLOAK: ${CLIENT_ID_KEYCLOAK}
      CLIENT_SECRET_KEYCLOAK: /run/secrets/client_secret_keycloak
      DB_HOST: ${DB_HOST}
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      BASE_API_URL: ${BASE_API_URL}
      REALM_NAME: ${REALM_NAME}
      FRONTEND_REDIRECT_URL: ${FRONTEND_REDIRECT_URL}
      SNAP_API_CLIENT_SECRET: /run/secrets/snap_api_client_secret
      CAPTCHA_KEY: /run/secrets/captcha_key
      REDIRECT_URI_KEYCLOAK: ${REDIRECT_URI_KEYCLOAK}
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
      MINIO_CONTAINER_NAME: ${MINIO_CONTAINER_NAME}
      MINIO_S3_INTERNAL_PORT: ${MINIO_S3_INTERNAL_PORT}
      KAFKA_CONTAINER_NAME: ${KAFKA_CONTAINER_NAME}
      KAFKA_EXTERNAL_URI: ${KAFKA_EXTERNAL_URI}
      KAFKA_INTERNAL_PORT: ${KAFKA_INTERNAL_PORT}
      KAFKA_EXTERNAL_PORT: ${KAFKA_EXTERNAL_PORT}
      SHOULD_USE_KAFKA_CONTAINER: ${SHOULD_USE_KAFKA_CONTAINER:-true}
      DELETE_PDFS_AFTER_DOWNLOAD: ${DELETE_PDFS_AFTER_DOWNLOAD:-true}
      LOG_LEVEL: ${LOG_LEVEL}
      STACK_NAME: mystack
      KEYCLOAK_VERIFY_SSL: "false"
      GUNICORN_WORKERS: ${GUNICORN_WORKERS:-3}
      GUNICORN_BIND: ${GUNICORN_BIND:-0.0.0.0:8000}
      GUNICORN_TIMEOUT: ${GUNICORN_TIMEOUT:-120}
      GUNICORN_GRACEFUL_TIMEOUT: ${GUNICORN_GRACEFUL_TIMEOUT:-30}
      GUNICORN_KEEPALIVE: ${GUNICORN_KEEPALIVE:-5}
      GUNICORN_MAX_REQUESTS: ${GUNICORN_MAX_REQUESTS:-0}
      GUNICORN_MAX_REQUESTS_JITTER: ${GUNICORN_MAX_REQUESTS_JITTER:-0}
      GUNICORN_LOG_LEVEL: ${GUNICORN_LOG_LEVEL:-info}
      GUNICORN_ACCESS_LOG: ${GUNICORN_ACCESS_LOG:--}
      # DB pool tuning
      DB_POOL_SIZE: ${DB_POOL_SIZE:-20}
      DB_MAX_OVERFLOW: ${DB_MAX_OVERFLOW:-30}
      DB_POOL_TIMEOUT: ${DB_POOL_TIMEOUT:-30}
      # Redis configuration
      REDIS_HOST: ${REDIS_HOST:-redis}
      REDIS_PORT: ${REDIS_PORT:-6379}
      REDIS_DB: ${REDIS_DB:-0}
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      REDIS_SSL: ${REDIS_SSL:-false}
      REDIS_MAX_CONNECTIONS: ${REDIS_MAX_CONNECTIONS:-10}
      # Migration configuration
      AUTO_GENERATE_MIGRATIONS: ${AUTO_GENERATE_MIGRATIONS:-true}

    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - kafka
      - redis
      - keycloak
    secrets:
      - snap_api_client_secret
      - client_secret_keycloak
      - captcha_key

    deploy:
      resources:
        limits:
          cpus: "1"
          memory: "512M"
        reservations:
          cpus: "0.6"
          memory: "256M"
    restart: unless-stopped

    networks:
      - mystack-net

# Migration is now handled automatically by the FastAPI auto-initialization system
# The migrate container has been removed to avoid duplicate migrations
  #networks:
  #  mystack-net:
  #    external: true

  #secrets:
  #  snap_api_client_secret:
  #    external: true
  #  captcha_key:
  #    external: true
  #  client_secret_keycloak:
  #    external: true

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 5s
      
      timeout: 3s
      retries: 5
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: "512M"
        reservations:
          cpus: "0.2"
          memory: "128M"
    networks:
      - mystack-net

volumes:
  redis_data:

networks:
  mystack-net:
    driver: overlay
