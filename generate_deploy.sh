#!/bin/bash
set -euo pipefail

# Prevent Git Bash on Windows from converting Unix paths to Windows paths
export MSYS_NO_PATHCONV=1

# Save current directory and restore on exit
ORIGINAL_DIR="$(pwd)"
trap 'cd "$ORIGINAL_DIR"' EXIT
cd "$(dirname "$0")"

# Default values
STACK_NAME=mystack
NETWORK_NAME=mystack-net
PROFILE="prod"
CLIENT_ID_GOOGLE=""
CLIENT_SECRET_GOOGLE=""
KEYCLOAK_ADMIN_PASSWORD=""
CLIENT_SECRET_NAME=client_secret_keycloak
ADMIN_USER_SECRET_NAME=keycloak_admin_user
ADMIN_PASSWORD_SECRET_NAME=keycloak_admin_password
SNAP_API_CLIENT_SECRET_NAME=snap_api_client_secret
CAPTCHA_KEY_NAME=captcha_key
CLIENT_ID_GOOGLE=""
CLIENT_SECRET_GOOGLE=""
CLIENT_ID_MICROSOFT=""
CLIENT_SECRET_MICROSOFT=""
MICROSOFT_TENANT="common"
KEYCLOAK_ADMIN_USER="admin"
KEYCLOAK_ADMIN_PASSWORD=""
SNAP_API_CLIENT_SECRET=""
CAPTCHA_KEY=""
CLIENT_SECRET_KEYCLOAK=""
args=(
STACK_NAME
NETWORK_NAME
PROFILE
CLIENT_ID_GOOGLE
CLIENT_SECRET_GOOGLE
KEYCLOAK_ADMIN_PASSWORD
CLIENT_SECRET_NAME
ADMIN_USER_SECRET_NAME
ADMIN_PASSWORD_SECRET_NAME
SNAP_API_CLIENT_SECRET_NAME
CAPTCHA_KEY_NAME
CLIENT_ID_GOOGLE
CLIENT_SECRET_GOOGLE
CLIENT_ID_MICROSOFT
CLIENT_SECRET_MICROSOFT
MICROSOFT_TENANT
KEYCLOAK_ADMIN_USER
KEYCLOAK_ADMIN_PASSWORD
SNAP_API_CLIENT_SECRET
CAPTCHA_KEY
)


print_title() {
  echo ""
  echo "===================="
  echo "$1"
  echo "===================="
}

print_success() {
  echo "✅ $1"
}

print_info() {
  echo "ℹ️ $1"
}

print_warning() {
  echo "⚠️ $1"
}

print_error_and_exit() {
  echo "❌ $1"
  echo "exiting"
  exit 1
}

print_args() {
  print_title "Parsed args:"
  for var_name in "${args[@]}"; do
    value="${!var_name}"  # Indirect expansion to get the value
    echo "$var_name = $value"
  done

  echo ""
  print_title "Full environment:"
  env | sort
}

setup_keycloak_env() {
  print_title "Setting up Internal Keycloak Environment"
  
  # Keycloak configuration is now handled by environment files:
  # - config/keycloak-production.env
  # - config/keycloak-development.env
  # These files contain the proper URLs for reportsbeta.../auth
  
  # Set database environment (required for Keycloak)
  export DB_HOST=${DB_HOST:-postgres}
  export DB_NAME=${DB_NAME:-keycloak}
  export DB_USER=${KC_DB_USERNAME:-keycloak}
  export DB_PASS=${DB_PASS:-keycloak}

  # Set Keycloak client configuration
  export CLIENT_ID_KEYCLOAK=${CLIENT_ID_KEYCLOAK:-myclient}
  export REALM_NAME=${REALM_NAME:-SnapReportsRealm}
  export KC_HTTP_RELATIVE_PATH=${KC_HTTP_RELATIVE_PATH:-/}
  # Set additional environment variables for backend
  export BASE_API_URL=${BASE_API_URL:-https://reportsbeta.snapforensics.com/reports}
  export FRONTEND_REDIRECT_URL=${FRONTEND_REDIRECT_URL:-https://reportsbeta.snapforensics.com}
  export REDIRECT_URI_KEYCLOAK=${REDIRECT_URI_KEYCLOAK:-https://reportsbeta.snapforensics.com/auth/callback}
  
  case "$PROFILE" in
    "prod"|"prod-dev"|"prod-staging")
      print_info "Using production Keycloak configuration (internal-only with reportsbeta.../auth)"
      print_info "Environment variables will be provided by pipeline"
      # Set essential database settings for production
      export POSTGRES_SSL=${POSTGRES_SSL:-off}
      export KC_DB_URL_PROPERTIES=${KC_DB_URL_PROPERTIES:-?sslmode=disable}
      ;;
    "dev")
      print_info "Loading development Keycloak configuration (internal-only with reportsbeta.../auth)"

  esac
  
  print_success "Internal Keycloak environment configured"
}

validate_env() {
  # Required secrets
  print_title "Validating variables"

  [[ -z "$KEYCLOAK_ADMIN_PASSWORD" ]] && print_error_and_exit "--keycloak-admin-password is required."
  [[ -z "$SNAP_API_CLIENT_SECRET" ]] && print_error_and_exit "--snap-api-client-secret is required."
  [[ -z "$CAPTCHA_KEY" ]] && print_error_and_exit "--captcha-key is required."

  print_info "Using Keycloak admin username: $KEYCLOAK_ADMIN_USER"

  # Google OAuth pair validation
  if { [ -n "$CLIENT_ID_GOOGLE" ] && [ -z "$CLIENT_SECRET_GOOGLE" ]; } || \
     { [ -z "$CLIENT_ID_GOOGLE" ] && [ -n "$CLIENT_SECRET_GOOGLE" ]; }; then
      print_error_and_exit "Both --client-id-google and --client-secret-google must be provided together."
  fi

  # Microsoft OAuth + tenant validation
  if [ "$MICROSOFT_TENANT" != "common" ]; then
      echo "🔐 Microsoft Tenant restriction: $MICROSOFT_TENANT"
      if [[ -z "$CLIENT_ID_MICROSOFT" || -z "$CLIENT_SECRET_MICROSOFT" ]]; then
          print_error_and_exit "Tenant specified but Microsoft client creds missing."
      fi
  fi

  if { [ -n "$CLIENT_ID_MICROSOFT" ] && [ -z "$CLIENT_SECRET_MICROSOFT" ]; } || \
     { [ -z "$CLIENT_ID_MICROSOFT" ] && [ -n "$CLIENT_SECRET_MICROSOFT" ]; }; then
      print_error_and_exit "Both --client-id-microsoft and --client-secret-microsoft must be provided together."
  fi

  if [[ -z "$CLIENT_ID_GOOGLE" && -z "$CLIENT_ID_MICROSOFT" ]]; then
      print_error_and_exit "You must provide at least one provider (Google or Microsoft)."
  fi

  print_success "Input validated successfully!"

}

load_env(){

if [[ -f $1 ]]; then
      echo "📁 Loading $1 file, dev profile"
      print_info "Loading .env: $1"
      set -a
      # shellcheck source=.env
      source "$1"
      set +a
    else
      print_error_and_exit "❌ .env file not found for dev profile"
    fi

}

argument_parse(){
  # Argument parsing

  print_title "Parsing arguments..."

  while [[ $# -gt 0 ]]; do
    case "$1" in
      --profile) PROFILE="$2"; shift 2 ;;
      --client-id-google) CLIENT_ID_GOOGLE="$2"; shift 2 ;;
      --client-secret-google) CLIENT_SECRET_GOOGLE="$2"; shift 2 ;;
      --client-id-microsoft) CLIENT_ID_MICROSOFT="$2"; shift 2 ;;
      --client-secret-microsoft) CLIENT_SECRET_MICROSOFT="$2"; shift 2 ;;
      --microsoft-tenant) MICROSOFT_TENANT="$2"; shift 2 ;;
      --keycloak-admin-user) KEYCLOAK_ADMIN_USER="$2"; shift 2 ;;
      --keycloak-admin-password) KEYCLOAK_ADMIN_PASSWORD="$2"; shift 2 ;;
      --snap-api-client-secret) SNAP_API_CLIENT_SECRET="$2"; shift 2 ;;
      --captcha-key) CAPTCHA_KEY="$2"; shift 2 ;;
      *) print_error_and_exit "❌ Unknown option while parsing: $1" ;;
    esac
  done

  if [[ "$PROFILE" == "dev" || "$PROFILE" == "back-dev" || "$PROFILE" == "prod-dev" ]]; then
    load_env ".env"
  fi

  if [[ "$PROFILE" == "prod-dev" ]]; then
      load_env ".env.prod.dev"
  fi

  print_success "Arguments parsed"
}

generate_secret() {
    openssl rand -base64 32 | tr -d '\n'
}

create_a_secret() {
    local SECRET_NAME=$1
    local SECRET_VALUE=$2

    # Check if secret already exists
    if docker secret ls --filter name="$SECRET_NAME" -q | grep -q .; then
        echo "⚠️ Secret $SECRET_NAME already exists. Skipping creation."
        return
    fi

    # Use echo with pipe instead of temporary file for Windows compatibility
    print_info "Creating secret $SECRET_NAME..."
    if echo -n "$SECRET_VALUE" | docker secret create "$SECRET_NAME" -; then
        print_success "Docker secret $SECRET_NAME created!"
    else
        print_error_and_exit "Failed to create secret $SECRET_NAME."
    fi
}

create_secrets(){

  # Always create/recreate the secret and treat as new deployment
  SKIP_KC_REALM_SECRET=false
  if [[ -z "$CLIENT_SECRET_KEYCLOAK" ]]; then
    CLIENT_SECRET_KEYCLOAK=$(generate_secret)
    print_info "Generated CLIENT_SECRET_KEYCLOAK: $CLIENT_SECRET_KEYCLOAK"
  fi
  
  # Force remove and recreate CLIENT_SECRET_KEYCLOAK for back-dev and prod-dev profiles
  if [[ "$PROFILE" == "back-dev" || "$PROFILE" == "prod-dev" ]]; then
    if docker secret inspect "$CLIENT_SECRET_NAME" &>/dev/null; then
      print_info "$PROFILE: Force removing existing CLIENT_SECRET_KEYCLOAK to update with new value"
      docker secret rm "$CLIENT_SECRET_NAME" >/dev/null || true
    fi
  fi
  
  create_a_secret "$CLIENT_SECRET_NAME" "$CLIENT_SECRET_KEYCLOAK"
  # Always create marker file to indicate new deployment for realm import
  echo "new" > apps/keycloak/deployment_type.txt

  secrets=(
  "$ADMIN_USER_SECRET_NAME"         "$KEYCLOAK_ADMIN_USER"
  "$ADMIN_PASSWORD_SECRET_NAME"     "$KEYCLOAK_ADMIN_PASSWORD"
  "$SNAP_API_CLIENT_SECRET_NAME"    "$SNAP_API_CLIENT_SECRET"
  "$CAPTCHA_KEY_NAME"               "$CAPTCHA_KEY"
)

  for ((i=0; i<${#secrets[@]}; i+=2)); do
    name="${secrets[i]}"
    value="${secrets[i+1]}"

    if [[ -z "$name" || -z "$value" ]]; then
      echo "⚠️ Skipping empty secret name or value: [$name] [$value]"
      continue
    fi

    if docker secret inspect "$name" &>/dev/null; then
      print_info "Removing existing secret: $name"
      docker secret rm "$name" >/dev/null
    else
      print_info "Secret $name not found, skipping removal."
    fi

    print_info "Creating secret $name"
    create_a_secret "$name" "$value"
  done

}

create_keycloak_conf(){
  if [ "$SKIP_KC_REALM_SECRET" = false ]; then
    print_title "Injecting secrets into realm.json"

    ESC_KC_SECRET=$(printf '%s\n' "$CLIENT_SECRET_KEYCLOAK" | sed 's/[&/\]/\\&/g')
    ESC_GOOGLE_ID=$(printf '%s\n' "$CLIENT_ID_GOOGLE" | sed 's/[&/\]/\\&/g')
    ESC_GOOGLE_SECRET=$(printf '%s\n' "$CLIENT_SECRET_GOOGLE" | sed 's/[&/\]/\\&/g')
    ESC_MS_ID=$(printf '%s\n' "$CLIENT_ID_MICROSOFT" | sed 's/[&/\]/\\&/g')
    ESC_MS_SECRET=$(printf '%s\n' "$CLIENT_SECRET_MICROSOFT" | sed 's/[&/\]/\\&/g')
    ESC_TENANT=$(printf '%s\n' "$MICROSOFT_TENANT" | sed 's/[&/\]/\\&/g')

    mkdir -p apps/keycloak

    sed -e "s/CLIENT_SECRET_KEYCLOAK/$ESC_KC_SECRET/g" \
        -e "s/CLIENT_ID_GOOGLE/$ESC_GOOGLE_ID/g" \
        -e "s/CLIENT_SECRET_GOOGLE/$ESC_GOOGLE_SECRET/g" \
        -e "s/CLIENT_ID_MICROSOFT/$ESC_MS_ID/g" \
        -e "s/CLIENT_SECRET_MICROSOFT/$ESC_MS_SECRET/g" \
        -e "s/MICROSOFT_TENANT/$ESC_TENANT/g" \
        apps/keycloak/realm.json.template > apps/keycloak/realm.json

    print_success "realm.json generated successfully"
  else
    print_info "Skipping realm.json update for Keycloak client secret."
  fi
}

build_images() {
  print_title "Building Docker Images"

  extra_args=""

  # Optional: Validate required variables for dev
  if [[ "$PROFILE" == "dev" || "$PROFILE" == "prod-dev" ]]; then
    extra_args=(--progress=plain)
    if [[ -z "${VITE_REPORTS_API_URL:-}" || -z "${VITE_PDF_SERVICE_URL:-}" ]]; then
      print_error_and_exit "Missing VITE_REPORTS_API_URL or VITE_PDF_SERVICE_URL for dev build."
    fi
  fi

  export DOCKER_BUILDKIT=1

  # Common image builds
  # docker pull python:3.12-slim
  # docker pull node:24
  # docker pull nginx:alpine
  # docker pull apache/kafka:4.1.0
  # docker pull quay.io/keycloak/keycloak:25.0.6
  # docker pull minio/minio:RELEASE.2025-04-22T22-12-26Z
 
  docker build -t my-backend-image -f apps/backend/Dockerfile . $extra_args
 
  # Build PDF service for profiles that need it
  if [[ "$PROFILE" == "prod" || "$PROFILE" == "prod-dev" || "$PROFILE" == "prod-staging" || "$PROFILE" == "dev" ]]; then
    docker build -t my-pdf-image -f apps/pdf-service/Dockerfile .
    docker build -t my-docx-image -f apps/docx-service/Dockerfile .
  fi


  case "$PROFILE" in
    "prod"|"prod-staging")
      print_info "Building frontend with VITE_REPORTS_API_URL=$VITE_REPORTS_API_URL"
      print_info "Building frontend with VITE_PDF_SERVICE_URL=$VITE_PDF_SERVICE_URL"
      print_info "Building frontend with VITE_DOCX_SERVICE_URL=$VITE_DOCX_SERVICE_URL"
      docker build --no-cache \
        --build-arg VITE_REPORTS_API_URL="$VITE_REPORTS_API_URL" \
        --build-arg VITE_PDF_SERVICE_URL="$VITE_PDF_SERVICE_URL" \
        --build-arg VITE_DOCX_SERVICE_URL="$VITE_DOCX_SERVICE_URL" \
        -f apps/frontend/Dockerfile \
        -t my-frontend-image . $extra_args
      ;;

    "prod-dev")
      print_info "Building frontend with VITE_REPORTS_API_URL=$VITE_REPORTS_API_URL"
      print_info "Building frontend with VITE_PDF_SERVICE_URL=$VITE_PDF_SERVICE_URL"
      print_info "Building frontend with VITE_DOCX_SERVICE_URL=$VITE_DOCX_SERVICE_URL"
      docker build \
        --build-arg VITE_REPORTS_API_URL="$VITE_REPORTS_API_URL" \
        --build-arg VITE_PDF_SERVICE_URL="$VITE_PDF_SERVICE_URL" \
        --build-arg VITE_DOCX_SERVICE_URL="$VITE_DOCX_SERVICE_URL" \
        -f apps/frontend/Dockerfile \
        -t my-frontend-image . $extra_args

      # Build Keycloak with internal-only configuration
      print_info "Building Keycloak image (internal-only configuration)"

      # docker build -t my-keycloak-image ./apps/keycloak --build-arg KC_HTTP_RELATIVE_PATH="$KC_HTTP_RELATIVE_PATH" $extra_args
      
      docker build -t my-keycloak-image ./apps/keycloak $extra_args
      
      docker build -t my-kafka-image ./apps/kafka $extra_args
      docker build -t my-minio-image -f apps/minio/Dockerfile . $extra_args
      docker build -t my-processor-image -f apps/processor/Dockerfile . $extra_args
      # docker build -t my-observability-image -f apps/observability/Dockerfile . $extra_args
      docker build -t my-autoscaler-image -f apps/autoscaler/Dockerfile . $extra_args
      ;;

    "dev")
      print_info "Building frontend with VITE_REPORTS_API_URL=$VITE_REPORTS_API_URL"
      print_info "Building frontend with VITE_PDF_SERVICE_URL=$VITE_PDF_SERVICE_URL"
      print_info "Building frontend with VITE_DOCX_SERVICE_URL=$VITE_DOCX_SERVICE_URL"

      docker build \
        --build-arg VITE_REPORTS_API_URL="$VITE_REPORTS_API_URL" \
        --build-arg VITE_PDF_SERVICE_URL="$VITE_PDF_SERVICE_URL" \
        --build-arg VITE_DOCX_SERVICE_URL="$VITE_DOCX_SERVICE_URL" \
        -f apps/frontend/Dockerfile \
        -t my-frontend-image . $extra_args
      
      # Build Keycloak for development (internal-only)
      print_info "Building Keycloak image (development internal-only)"
      docker build -t my-keycloak-image ./apps/keycloak --build-arg KC_HTTP_RELATIVE_PATH="$KC_HTTP_RELATIVE_PATH" $extra_args
      
      docker build -t my-kafka-image ./apps/kafka $extra_args
      docker build -t my-minio-image -f apps/minio/Dockerfile . $extra_args
      ;;
    "back-dev")
    # intentionally no-op; handled elsewhere or not needed
    ;;
    *)
      print_error_and_exit "Unknown PROFILE: '$PROFILE'. Expected 'dev' 'back-dev' or 'prod'."
      ;;
  esac

  print_success "Docker images built successfully for profile: $PROFILE"
}

show_keycloak_access_info() {
  print_title "🔒 Keycloak Deployment Information (VM *************)"

  case "$PROFILE" in
    "prod"|"prod-staging")
      echo "🏭 Production Keycloak + Frontend Nginx Reverse Proxy:"
      echo "  External User URL: https://reportsbeta.snapforensics.com/authkc/"
      echo "  Internal Keycloak: http://keycloak:8080 (container-only)"
      echo "  VM IP: *************"
      echo ""
      echo "🔒 ADMIN CONSOLE ACCESS (VM/VPN ONLY):"
      echo "  Method 1 - SSH Tunnel: ssh -L 8080:keycloak:8080 user@*************"
      echo "           Then access: http://localhost:8080/admin/"
      echo "  Method 2 - VPN: Connect to VM network, then http://keycloak:8080/admin/"
      echo "  Method 3 - VM Direct: From VM: http://localhost:8080/admin/"
      echo ""
      echo "🔧 Setup Requirements:"
      echo "  1. DNS: Point reportsbeta.snapforensics.com to *************"
      echo "  2. SSL Certificate: Run ./scripts/setup-ssl-certificates.sh"
      echo "  3. Firewall: Open ports 80, 443 on VM"
      ;;
    "dev")
      echo "🧪 Development Keycloak + Ngrok Tunnels:"
      echo "  External URLs: Provided by ngrok (dynamic)"
      echo "  Internal Keycloak: http://keycloak:8080 (container-only)"
      echo "  VM IP: *************"
      echo ""
      echo "🔒 ADMIN CONSOLE ACCESS (VM/VPN ONLY):"
      echo "  Method 1 - SSH Tunnel: ssh -L 8080:keycloak:8080 user@*************"
      echo "           Then access: http://localhost:8080/admin/"
      echo "  Method 2 - VPN: Connect to VM network, then http://keycloak:8080/admin/"
      echo "  Method 3 - VM Direct: From VM: http://localhost:8080/admin/"
      echo ""
      echo "🔧 Development Setup:"
      echo "  1. Ngrok tunnels will provide dynamic URLs"
      echo "  2. Admin access only via SSH tunnel or VPN"
      ;;
  esac

  echo ""
  echo "💡 Application Integration:"
  echo "  Backend Services: Use KEYCLOAK_URL=http://keycloak:8080"
  echo "  Frontend Nginx: Handles reverse proxy to Keycloak"
  echo "  External Users: Access via https://reportsbeta.snapforensics.com/authkc/"
  echo ""
  echo "⚠️  SECURITY NOTICE:"
  echo "  Admin console is BLOCKED from external access in frontend nginx"
  echo "  Only accessible via VM/VPN for maximum security"
  echo ""
  print_success "Keycloak deployed with maximum admin security!"
}

deploy_stack() {

  print_title "Deploying Docker Stack: $STACK_NAME (Environment: $PROFILE)"

  case "$PROFILE" in
  dev)
    # Development with ngrok tunnels - no fixed URLs needed
    # Development with ngrok tunnels - no fixed URLs needed
    COMPOSE_FILES=(
      "docker-compose.yml"
      "docker-compose-postgres.yml"
      "docker-compose.yml"
      "docker-compose-postgres.yml"
      "docker-compose-backend.yml"
      "docker-compose-keycloak.yml"
      "docker-compose-pdf.yml"
      "docker-compose-keycloak.yml"
      "docker-compose-pdf.yml"
      "docker-compose-frontend.yml"
      "docker-compose-ngrok.yml"                 # Ngrok tunnels for development
      "docker-compose-docx.yml"
      "docker-compose-kafka.yml"
      "docker-compose-minio.yml"
      "docker-compose-backend-dev.yml"
      "docker-compose-frontend-dev.yml"
    )
    docker network create --driver overlay --attachable "$NETWORK_NAME" || true
    ;;

  back-dev)
    # Backend-only development
    # Backend-only development
    COMPOSE_FILES=(
      "docker-compose-backend.yml"
      "docker-compose-backend-dev.yml"
    )
    docker network create --driver overlay --attachable "$NETWORK_NAME" || true
    ;;

  prod)
    # Production stack with internal-only Keycloak (uses frontend nginx)
    # Production stack with internal-only Keycloak (uses frontend nginx)
    COMPOSE_FILES=(
      "docker-compose.yml"
      "docker-compose-postgres.yml"
      "docker-compose-backend.yml"
      "docker-compose-keycloak.yml"
      "docker-compose-pdf.yml"
      "docker-compose-docx.yml"
      "docker-compose-frontend.yml"            # Frontend nginx handles reverse proxy
      "docker-compose-letsencrypt.yml"        # Let's Encrypt for SSL certs
      # "docker-compose-ngrok.yml"
      "docker-compose-kafka.yml"
      "docker-compose-minio.yml"
      "docker-compose-processor.yml"
      "docker-compose-observability.yml"
      "docker-compose-autoscaler.yml"
    )
    ;;
  prod-staging)
  # Production stack with internal-only Keycloak (uses frontend nginx)
    COMPOSE_FILES=(
    "docker-compose.yml"
    "docker-compose-postgres.yml"
    "docker-compose-backend.yml"
    "docker-compose-keycloak.yml"
    "docker-compose-pdf.yml"
    "docker-compose-docx.yml"
    "docker-compose-frontend.yml"            # Frontend nginx handles reverse proxy
    "docker-compose-ngrok.yml"
    "docker-compose-kafka.yml"
    "docker-compose-minio.yml"
    "docker-compose-processor.yml"
    "docker-compose-observability.yml"
    "docker-compose-autoscaler.yml"
  )
  ;;
  prod-dev)
    # Production-development with internal-only Keycloak
    COMPOSE_FILES=(
      "docker-compose.yml"
      "docker-compose-postgres.yml"
      "docker-compose-backend.yml"
      "docker-compose-keycloak.yml"
      "docker-compose-pdf.yml"
      "docker-compose-docx.yml"
      "docker-compose-frontend.yml"
      "docker-compose-ngrok.yml"
      "docker-compose-kafka.yml"
      "docker-compose-minio.yml"
      "docker-compose-processor.yml"
      "docker-compose-observability.yml"
      "docker-compose-autoscaler.yml"
    )


#    docker compose --env-file $env_file $(printf " -f %s" "${COMPOSE_FILES[@]}") config > "merged-stack.yml"
#    COMPOSE_FILES=(
#      "merged-stack.yml"
#    )

#    docker compose --env-file .env -f docker-compose-spark.yml config > merged.yml
esac

    # Construct the deploy command
    local DEPLOY_CMD="docker stack deploy"
    for file in "${COMPOSE_FILES[@]}"; do
        DEPLOY_CMD+=" -c $file"
    done
    DEPLOY_CMD+=" $STACK_NAME"

    # Output the deploy command for clarity
    eval "$DEPLOY_CMD"
    print_success "Deployed successfully!"
}

docker_stack_cleanup() {
  print_title "Clearing stack before deployment: $STACK_NAME"

  # Remove stack if exists
  if docker stack ls | grep -q "$STACK_NAME"; then
    print_info "Removing existing Docker Stack: $STACK_NAME"
    docker stack rm "$STACK_NAME"

    print_info "Waiting for Docker stack '$STACK_NAME' to shut down..."
    while docker stack services "$STACK_NAME" 2>&1 | grep -vq "Nothing found in stack"; do
      print_info "Still shutting down..."; sleep 2
    done
    print_success "Stack '$STACK_NAME' is fully removed."
  else
    print_success "Stack '$STACK_NAME' does not exist."
  fi

  # Remove conflicting Docker network
  network_id=$(docker network ls --filter name="^$NETWORK_NAME$" -q)

  if [ -n "$network_id" ]; then
    print_info "Removing existing Docker network: $NETWORK_NAME"
    echo "⚠️ Found network $NETWORK_NAME with ID: $network_id"

    attached_containers=$(docker network inspect "$network_id" -f '{{range .Containers}}{{.Name}} {{end}}')

    if [ -n "$attached_containers" ]; then
      for container in $attached_containers; do
        print_info "Forcibly removing container $container from network"
        docker rm -f "$container" || true
      done
    fi

    print_info "🧹 Removing the network..."
    docker network rm "$network_id" && print_success "✅ Network removed." || print_info "❌ Failed to remove network."

    print_info "⏳ Waiting for network $NETWORK_NAME to be fully removed..."
    while docker network ls | grep -q "$network_id"; do
      print_info "Still removing network $NETWORK_NAME..."; sleep 2
    done
    print_success "Network $NETWORK_NAME fully removed."
  else
    print_success "✅ No existing $NETWORK_NAME network found."
  fi
}

configure_keycloak_mappers() {
  print_title "Configuring Keycloak Protocol Mappers"

  # Wait for Keycloak to be ready
  print_info "Waiting for Keycloak service to be ready..."
  sleep 30

  # Check if Keycloak container is running
  KC_CONTAINER=$(docker ps --filter "name=mystack_keycloak" -q)
  if [ -z "$KC_CONTAINER" ]; then
    print_warning "Keycloak container not found. Skipping mapper configuration."
    return
  fi

  # Configure admin CLI
  print_info "Configuring Keycloak admin CLI..."
  docker exec -i $KC_CONTAINER /opt/keycloak/bin/kcadm.sh config credentials \
    --server http://localhost:8080 \
    --realm master \
    --user admin \
    --password $KEYCLOAK_ADMIN_PASSWORD 2>/dev/null || {
    print_warning "Failed to configure admin CLI. Keycloak may not be ready yet."
    return
  }

  # Get client internal ID
  print_info "Getting client ID for myclient..."
  CLIENT_UUID=$(docker exec -i $KC_CONTAINER /opt/keycloak/bin/kcadm.sh get clients \
    -r SnapReportsRealm \
    -q clientId=myclient \
    --fields id 2>/dev/null | grep '"id"' | cut -d'"' -f4)

  if [ -z "$CLIENT_UUID" ]; then
    print_warning "Client 'myclient' not found. Skipping mapper configuration."
    return
  fi

  # Check if User ID mapper already exists
  EXISTING_MAPPER=$(docker exec -i $KC_CONTAINER /opt/keycloak/bin/kcadm.sh get \
    clients/$CLIENT_UUID/protocol-mappers/models \
    -r SnapReportsRealm 2>/dev/null | grep '"name" : "User ID"' || true)

  if [ -n "$EXISTING_MAPPER" ]; then
    print_info "User ID mapper already exists. Skipping creation."
    return
  fi

  # Create User ID mapper
  print_info "Creating User ID protocol mapper..."
  docker exec -i $KC_CONTAINER /opt/keycloak/bin/kcadm.sh create \
    clients/$CLIENT_UUID/protocol-mappers/models \
    -r SnapReportsRealm \
    -s name="User ID" \
    -s protocol=openid-connect \
    -s protocolMapper=oidc-usermodel-property-mapper \
    -s 'config."user.attribute"=id' \
    -s 'config."claim.name"=sub' \
    -s 'config."access.token.claim"=true' \
    -s 'config."id.token.claim"=true' \
    -s 'config."userinfo.token.claim"=true' \
    -s 'config."jsonType.label"=String' 2>/dev/null && {
    print_success "User ID mapper created successfully!"
  } || {
    print_warning "Failed to create User ID mapper. May already exist or Keycloak not ready."
  }
}


argument_parse "$@"
print_args
validate_env
setup_keycloak_env

# Init Swarm
docker swarm init 2>/dev/null || true
# Remove stack
docker_stack_cleanup
create_secrets
create_keycloak_conf
build_images
deploy_stack

# Configure Keycloak mappers
configure_keycloak_mappers

# Show Keycloak access information
show_keycloak_access_info
