import { Text } from "@snap/design-system";
import { LuTrash2 } from "react-icons/lu";
import { ReportMetadata, ReportSection } from "~/types/global";
import { __PersonDetailsPage } from "root/modules/@snap/reports/ui/containers/report/";
import { REPORT_DETAIL_STORE_INSTANCE } from "~/store/details";
import { useReportActionsWithAutoSave } from "~/hooks/useReportActionsWithAutoSave";
import { useMemo } from "react";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { extractCombinadoSectionsForTrash } from "~/helpers";

export const ReportTrash = () => {
  const {
    selectors: {
      useReportMetadata,
      useReportSections,
      useReportType,
      useIsActionLoading,
    }
  } = REPORT_DETAIL_STORE_INSTANCE;

  const metadata = useReportMetadata();
  const reportType = useReportType();
  const allSections = useReportSections();
  const isActionLoading = useIsActionLoading();
  const actions = useReportActionsWithAutoSave();

  const hasAnyDeleted = (obj: any): boolean => {
    if (obj && typeof obj === "object") {
      if (obj.is_deleted === true) return true;
      return Object.values(obj).some(hasAnyDeleted);
    }
    return false;
  }

  const trashSections = useMemo(
    () => {
      const sectionsToProcess = reportType === REPORT_CONSTANTS.types.combinado
        ? extractCombinadoSectionsForTrash(allSections)
        : allSections;

      return sectionsToProcess.filter((sec: ReportSection) => {
        if (sec.is_deleted === true || hasAnyDeleted(sec.data)) {
          return true;
        }

        if (sec.title === "Processos" && sec.subsection) {
          const mainProcessosSection = sectionsToProcess.find(
            (s: ReportSection) => s.title === "Processos" && !s.subsection
          );

          if (mainProcessosSection) {
            return sec.data.some((subsecEntry: any) => {
              const mainProcesso = mainProcessosSection.data.find(
                (p: any) => p.numero?.value === subsecEntry.numero?.value
              );
              return mainProcesso && hasAnyDeleted(mainProcesso);
            });
          }
        }

        return false;
      });
    },
    [allSections, reportType]
  );

  const storeProps = useMemo(() => ({
    sections: trashSections,
    metadata: metadata as ReportMetadata,
    reportType: reportType,
    isTrashEnabled: true,
    isPrintEnabled: false,
    isActionLoading: isActionLoading,
    actions
  }), [trashSections, metadata, reportType, isActionLoading, actions]);

  const renderContent = () => {
    if (!trashSections.length) {
      return (
        <div className="flex items-center justify-center min-h-[calc(100vh-300px)]">
          <div className="flex flex-col items-center gap-2 text-border py-8">
            <Text variant="label-lg" align="center"> A Lixeira está vazia</Text>
            <LuTrash2 size={56} />
          </div>
        </div>
      )
    }

    return (
      <div className="max-h-[calc(100vh-300px)] min-h-[calc(100vh-300px)] overflow-y-auto overflow-x-hidden" id="report-content-trash">
        <__PersonDetailsPage store={storeProps} renderMode="trash" />
      </div>
    )
  }

  return renderContent()
}