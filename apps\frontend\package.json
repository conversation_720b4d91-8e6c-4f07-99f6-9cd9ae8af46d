{"name": "trem", "type": "module", "version": "0.0.1", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --fix", "prepare": "husky", "validate": "run-p check lint", "test": "vitest", "test:run": "vitest run", "test:staged": "vitest related --run", "test:ci": "vitest run --coverage", "test:e2e": "pnpm exec playwright test --ui", "test:e2e:ci": "cross-env CI=true pnpm exec playwright test --pass-with-no-tests", "test:e2e:install": "pnpm exec playwright install firefox", "precommit": "lint-staged"}, "dependencies": {"@grafana/faro-web-sdk": "^1.13.1", "@grafana/faro-web-tracing": "^1.13.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@snap/design-system": "1.2.4", "@tailwindcss/postcss": "^4.0.14", "@tailwindcss/vite": "^4.0.14", "@tanstack/react-query": "^5.66.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "immer": "^10.1.1", "inputmask": "^5.0.9", "js-cookie": "^3.0.5", "lucide-react": "^0.476.0", "masonic": "^4.0.1", "next-themes": "^0.4.4", "postcss": "^8.5.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-layout-masonry": "^1.2.0", "react-router": "^7.9.3", "react-use-websocket": "^4.13.0", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.14", "tailwindcss-animate": "^1.0.7", "vite": "^6.3.6", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/js": "^9.20.0", "@playwright/test": "^1.50.1", "@tanstack/react-query-devtools": "^5.66.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/inputmask": "^5.0.7", "@types/js-cookie": "^3.0.6", "@types/node": "^22.7.7", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "eslint": "^9.20.1", "eslint-plugin-react": "^7.37.4", "globals": "^15.14.0", "husky": "^9.1.6", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "npm-run-all": "^4.1.5", "typescript": "^5.7.3", "typescript-eslint": "^8.24.0", "vite-plugin-pwa": "^0.21.1", "vite-plugin-wasm": "^3.4.1", "vitest": "^3.0.5"}, "volta": {"node": "22.13.1", "pnpm": "9.3.0"}, "engines": {"node": ">=22.13.1", "pnpm": ">=10.5.1"}, "packageManager": "pnpm@10.5.1", "setupFilesAfterEnv": ["@testing-library/jest-dom/extend-expect"], "pnpm": {"overrides": {"form-data": "4.0.4"}}}