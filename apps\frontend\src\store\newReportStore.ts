import { create } from "zustand";

interface NewReportActions {
  setSelectedReportType: (type: string) => void;
  setReportInputValue: (value: string) => void;
  setDocument1Type: (type: string) => void;
  setDocument1Value: (value: string) => void;
  setDocument2Type: (type: string) => void;
  setDocument2Value: (value: string) => void;
  setReportMode: (mode: "individual" | "relacoes") => void;
  setIgnoreConsortiums: (ignore: boolean) => void;
  setIgnoreOtherContacts: (ignore: boolean) => void;
  clearNewReportValues: () => void;
}

interface NewReportState {
  selectedReportType: string;
  reportInputValue: string;
  document1Type: string;
  document1Value: string;
  document2Type: string;
  document2Value: string;
  reportMode: "individual" | "relacoes";
  ignoreConsortiums: boolean;
  ignoreOtherContacts: boolean;
  actions: NewReportActions;
}

const useNewReportStore = create<NewReportState>((set) => ({
  selectedReportType: "",
  reportInputValue: "",
  document1Type: "",
  document1Value: "",
  document2Type: "",
  document2Value: "",
  reportMode: "individual",
  ignoreConsortiums: false,
  ignoreOtherContacts: false,
  actions: {
    setSelectedReportType: (type: string) => set({
      selectedReportType: type,
      reportInputValue: "",
      document1Type: "",
      document1Value: "",
      document2Type: "",
      document2Value: ""
    }),
    setReportInputValue: (value: string) => set({ reportInputValue: value }),
    setDocument1Type: (type: string) => set({ document1Type: type, document1Value: "" }),
    setDocument1Value: (value: string) => set({ document1Value: value }),
    setDocument2Type: (type: string) => set({ document2Type: type, document2Value: "" }),
    setDocument2Value: (value: string) => set({ document2Value: value }),
    setReportMode: (mode: "individual" | "relacoes") => set({
      reportMode: mode,
      selectedReportType: mode === "relacoes" ? "relacoes" : "",
      reportInputValue: "",
      document1Type: "",
      document1Value: "",
      document2Type: "",
      document2Value: ""
    }),
    setIgnoreConsortiums: (ignore: boolean) => set({ ignoreConsortiums: ignore }),
    setIgnoreOtherContacts: (ignore: boolean) => set({ ignoreOtherContacts: ignore }),
    clearNewReportValues: () =>
      set({
        selectedReportType: "",
        reportInputValue: "",
        document1Type: "",
        document1Value: "",
        document2Type: "",
        document2Value: "",
        reportMode: "individual",
        ignoreConsortiums: false,
        ignoreOtherContacts: false
      }),
  },
}));

export const useNewReportSelectedType = () =>
  useNewReportStore((state) => state.selectedReportType);
export const useNewReportInputValue = () =>
  useNewReportStore((state) => state.reportInputValue);
export const useDocument1Type = () =>
  useNewReportStore((state) => state.document1Type);
export const useDocument1Value = () =>
  useNewReportStore((state) => state.document1Value);
export const useDocument2Type = () =>
  useNewReportStore((state) => state.document2Type);
export const useDocument2Value = () =>
  useNewReportStore((state) => state.document2Value);
export const useReportMode = () =>
  useNewReportStore((state) => state.reportMode);
export const useIgnoreConsortiums = () =>
  useNewReportStore((state) => state.ignoreConsortiums);
export const useIgnoreOtherContacts = () =>
  useNewReportStore((state) => state.ignoreOtherContacts);
export const useNewReportActions = () =>
  useNewReportStore((state) => state.actions);
