import { Input, Text } from "@snap/design-system";

interface FolderCreationFormProps {
  folderName: string;
  onFolderNameChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown: (event: React.KeyboardEvent) => void;
}

export function FolderCreationForm({
  folderName,
  onFolderNameChange,
  onKeyDown
}: FolderCreationFormProps) {
  return (
    <div className="flex flex-col gap-4">
      <div>
        <Text className="block mb-1">Digite o nome da pasta que deseja criar:</Text>
        <Input
          type="text"
          variant="outlined"
          value={folderName}
          placeholder="Nome da pasta"
          onChange={onFolderNameChange}
          onKeyDown={onKeyDown}
          className={`rounded-none border-0 w-full border-b-1 border-dashed pl-0 text-[16px]`}
          maxLength={100}
        />
        <div className="flex justify-end mt-1">
          <Text variant="body-sm" className="text-muted-foreground">
            {folderName.length}/100 caracteres
          </Text>
        </div>
      </div>
    </div>
  );
}
