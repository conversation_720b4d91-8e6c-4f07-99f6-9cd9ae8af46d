import { create } from "zustand";
import { ReportDetailMutations, ReportDetailState, UpdateFnPromise } from "./interface";
import { REPORT_DETAIL_INITIAL_STATE } from "./state";
import { produce } from "immer";
import { getGlobalForceSave } from "~/hooks/useReportActionsWithAutoSave";
import { REPORT_CONSTANTS } from "~/helpers/constants";

export const useReportDetailStore = create<ReportDetailState & { actions: ReportDetailMutations }>()(
  (set, get) => {
    const extractCombinadoSections = (sections: any[], selectedFilter: string) => {
      const combinadoContainer = sections.find((section) =>
        section.combined_data || section.report_sources_data
      );

      if (!combinadoContainer) return sections;

      const showCombinedData = selectedFilter === 'combined_data';

      if (showCombinedData && Array.isArray(combinadoContainer.combined_data)) {
        return combinadoContainer.combined_data;
      } else if (Array.isArray(combinadoContainer.report_sources_data)) {
        return combinadoContainer.report_sources_data.filter(
          (sourceSection: any) =>
            !!sourceSection && sourceSection.user_reports_id === selectedFilter
        );
      }

      return sections;
    };

    // Helper function to recalculate section list records
    const recalculateSectionListRecords = () => {
      const currentState = get();
      const { sections, reportType, selectedCombinedFilter } = currentState.data;

      // For combinado reports, extract the appropriate sections based on filter
      const sectionsToProcess = reportType === REPORT_CONSTANTS.types.combinado
        ? extractCombinadoSections(sections, selectedCombinedFilter)
        : sections;

      const sectionListRecords = sectionsToProcess
        .filter((section: any) => {
          if (section.subsection) return false;
          if (section.is_deleted) return false;
          if (section.data_count > 0) return true;

          // Algumas seções vazias vem com a chave "detalhes" vazia dentro de "data", por isso a verificação abaixo
          return Array.isArray(section.data) &&
            section.data.some((item: any) =>
              Array.isArray(item.detalhes) &&
              item.detalhes.length > 0
            );
        })
        .map((s: any) => ({ title: s.title, data_count: s.data_count }));

      const totalRecords = sectionListRecords.reduce((sum: number, section: any) => {
        const count = Number(section.data_count) || 0;
        return sum + count;
      }, 0);

      // Update the section list records and total records
      set((state) => ({
        ui: {
          ...state.ui,
          sectionListRecords,
          totalRecords
        },
      }));
    };

    return {
      ...REPORT_DETAIL_INITIAL_STATE,
      actions: {
        setReportSections: (sections) => {
          set((state) => ({
            data: { ...state.data, sections },
          }));
          recalculateSectionListRecords();
        },

        setDeletedSections: (deletedSections) => {
          set((state) => ({
            data: { ...state.data, deletedSections },
          }));
          recalculateSectionListRecords();
        },

        setReportType: (reportType) =>
          set((state) => ({
            data: { ...state.data, reportType },
          })),

        setMetadata: (metadata) =>
          set((state) => ({
            data: { ...state.data, metadata },
          })),

        setProfileImage: (profileImage) =>
          set((state) => ({
            data: { ...state.data, profileImage },
          })),

        setSelectedCombinedFilter: (selectedCombinedFilter) => {
          set((state) => ({
            data: { ...state.data, selectedCombinedFilter },
          }));
          recalculateSectionListRecords();
        },

        setCombinedReportSources: (combinedReportSources) =>
          set((state) => ({
            data: { ...state.data, combinedReportSources },
          })),

        setSectionListRecords: (sectionListRecords) =>
          set((state) => ({
            ui: { ...state.ui, sectionListRecords },
          })),

        setRelacoesSectionList: (relacoesSectionList) =>
          set((state) => ({
            ui: { ...state.ui, relacoesSectionList },
          })),

        setTotalRecords: (totalRecords) =>
          set((state) => ({
            ui: { ...state.ui, totalRecords },
          })),

        forceReload: () =>
          set((state) => ({
            ui: { ...state.ui, reloadTrigger: state.ui.reloadTrigger + 1 },
          })),

        setActionLoading: (loading) =>
          set((state) => ({
            ui: { ...state.ui, isActionLoading: loading },
          })),

        updateSectionEntries: (
          sectionTitle,
          updaterFn,
          testEntryDeletedFn,
          testSectionDeletedFn,
          calculateDataCountFn,
          includeSubsections = false,
          crossSectionUpdate
        ) => {
          // Começa marcando a ação como carregando
          set((state) => ({
            ui: { ...state.ui, isActionLoading: true },
          }));

          // Adicionar um delay antes de executar a atualização
          setTimeout(() => {
            set((state) =>
              produce(state, (draft) => {
                const { reportType, selectedCombinedFilter } = draft.data;
                let sectionsToUpdate: any[] = [];

                if (reportType === REPORT_CONSTANTS.types.combinado) {
                  const combinadoContainer = draft.data.sections.find((section: any) =>
                    section.combined_data || section.report_sources_data
                  ) as any;

                  if (combinadoContainer) {
                    const showCombinedData = selectedCombinedFilter === REPORT_CONSTANTS.combined_data_keys.combined_data;

                    if (showCombinedData && Array.isArray(combinadoContainer.combined_data)) {
                      sectionsToUpdate = includeSubsections
                        ? combinadoContainer.combined_data.filter((s: any) => s.title === sectionTitle)
                        : combinadoContainer.combined_data.filter((s: any) => s.title === sectionTitle && !s.subsection);
                    } else if (Array.isArray(combinadoContainer.report_sources_data)) {
                      sectionsToUpdate = includeSubsections
                        ? combinadoContainer.report_sources_data.filter((s: any) => s.title === sectionTitle && s.user_reports_id === selectedCombinedFilter)
                        : combinadoContainer.report_sources_data.filter((s: any) => s.title === sectionTitle && !s.subsection && s.user_reports_id === selectedCombinedFilter);
                    }
                  }
                } else {
                  sectionsToUpdate = includeSubsections
                    ? draft.data.sections.filter((s) => s.title === sectionTitle)
                    : draft.data.sections.filter((s) => s.title === sectionTitle && !s.subsection);
                }

                const matchingValues: any[] = [];

                sectionsToUpdate.forEach((section) => {
                  // 1) Executa a função de atualização em cada entrada da seção
                  section.data.forEach((entry: any, i: number) => {
                    updaterFn(entry as any, i);
                    if (crossSectionUpdate && (entry as any)[crossSectionUpdate.matchingProp]?.value) {
                      matchingValues.push((entry as any)[crossSectionUpdate.matchingProp].value);
                    }
                  });

                  // 2) Marca a seção como deletada se todas as entradas forem deletadas
                  section.is_deleted = testSectionDeletedFn(section);

                  if (calculateDataCountFn) {
                    section.data_count = calculateDataCountFn(section);
                  }
                });

                // Lidar com atualizações entre seções
                if (crossSectionUpdate && matchingValues.length > 0) {
                  let mainSection: any = null;

                  if (reportType === REPORT_CONSTANTS.types.combinado) {
                    const combinadoContainer = draft.data.sections.find((section: any) =>
                      section.combined_data || section.report_sources_data
                    ) as any;

                    if (combinadoContainer) {
                      const showCombinedData = selectedCombinedFilter === 'combined_data';
                      const sectionsArray = showCombinedData ? combinadoContainer.combined_data : combinadoContainer.report_sources_data;
                      mainSection = sectionsArray?.find((s: any) => s.title === sectionTitle && !s.subsection);
                    }
                  } else {
                    mainSection = draft.data.sections.find((s) => s.title === sectionTitle && !s.subsection);
                  }

                  if (mainSection) {
                    mainSection.data.forEach((entry: any, i: number) => {
                      if (matchingValues.includes((entry as any)[crossSectionUpdate.matchingProp]?.value)) {
                        crossSectionUpdate.updaterFn(entry as any, i);
                      }
                    });
                    mainSection.is_deleted = testSectionDeletedFn(mainSection);
                    if (calculateDataCountFn) {
                      mainSection.data_count = calculateDataCountFn(mainSection);
                    }
                  }
                }
              })
            );

            // Recalculate section list records after updating sections
            recalculateSectionListRecords();

            // Resetar o estado de loading com um delay
            setTimeout(() => {
              set((state) => ({
                ui: { ...state.ui, isActionLoading: false },
              }));
            }, 100);
          }, 200);
        },

        updateSubsectionWithMainSection: (
          sectionTitle,
          subsectionName,
          matchingProp,
          updaterFn,
          testEntryDeletedFn,
          testSectionDeletedFn,
          calculateDataCountFn
        ) => {
          set((state) => ({
            ui: { ...state.ui, isActionLoading: true },
          }));

          setTimeout(() => {
            set((state) =>
              produce(state, (draft) => {
                const { reportType, selectedCombinedFilter } = draft.data;
                let subsection: any = null;
                let mainSection: any = null;

                // Handle combinado reports differently
                if (reportType === REPORT_CONSTANTS.types.combinado) {
                  const combinadoContainer = draft.data.sections.find((section: any) =>
                    section.combined_data || section.report_sources_data
                  ) as any;

                  if (combinadoContainer) {
                    const showCombinedData = selectedCombinedFilter === 'combined_data';
                    const sectionsArray = showCombinedData ? combinadoContainer.combined_data : combinadoContainer.report_sources_data;

                    if (sectionsArray) {
                      subsection = sectionsArray.find(
                        (s: any) => s.title === sectionTitle && s.subsection === subsectionName
                      );
                      mainSection = sectionsArray.find(
                        (s: any) => s.title === sectionTitle && !s.subsection
                      );
                    }
                  }
                } else {
                  // Handle normal reports
                  subsection = draft.data.sections.find(
                    (s) => s.title === sectionTitle && s.subsection === subsectionName
                  );
                  mainSection = draft.data.sections.find(
                    (s) => s.title === sectionTitle && !s.subsection
                  );
                }

                if (!subsection || !mainSection) {
                  return;
                }

                // Encontra os valores correspondentes na subsection
                const matchingValues = subsection.data.map((entry: any) => entry[matchingProp]?.value).filter(Boolean);

                // Atualiza a subsection
                subsection.data.forEach((entry: any, i: number) => updaterFn(entry as any, i));
                subsection.is_deleted = testSectionDeletedFn(subsection);
                if (calculateDataCountFn) {
                  subsection.data_count = calculateDataCountFn(subsection);
                }

                // Atualiza as entradas correspondentes na seção principal
                mainSection.data.forEach((entry: any, i: number) => {
                  if (matchingValues.includes((entry as any)[matchingProp]?.value)) {
                    updaterFn(entry as any, i);
                  }
                });
                mainSection.is_deleted = testSectionDeletedFn(mainSection);
                if (calculateDataCountFn) {
                  mainSection.data_count = calculateDataCountFn(mainSection);
                }
              })
            );

            // Recalculate section list records after updating sections
            recalculateSectionListRecords();

            setTimeout(() => {
              set((state) => ({
                ui: { ...state.ui, isActionLoading: false },
              }));
            }, 100);
          }, 200);
        },

        scheduleAutoSave: () => {
          const state = get();
          set((state) => ({
            autoSave: {
              ...state.autoSave,
              hasPendingChanges: true,
            }
          }));

          if (state.autoSave.isPending && state.autoSave.mutation) {
            state.autoSave.mutation.reset();
            set((state) => ({
              autoSave: {
                ...state.autoSave,
                isPending: false,
              }
            }));
          }

          if (state.autoSave.timeoutId) {
            clearTimeout(state.autoSave.timeoutId);
          }

          const timeoutId = setTimeout(() => {
            const forceSave = getGlobalForceSave();
            if (forceSave) {
              forceSave();
            }
          }, 5000);

          set((state) => ({
            autoSave: {
              ...state.autoSave,
              timeoutId,
            }
          }));
        },

        cancelAutoSave: () => {
          const state = get();
          if (state.autoSave.timeoutId) {
            clearTimeout(state.autoSave.timeoutId);
          }
          set((state) => ({
            autoSave: {
              ...state.autoSave,
              timeoutId: null,
              hasPendingChanges: false,
            }
          }));
        },


        isPendingSave: () => get().autoSave.isPending,

        hasPendingChanges: () => get().autoSave.hasPendingChanges,

        setAutoSaveMutation: (mutation) =>
          set((state) => ({
            autoSave: { ...state.autoSave, mutation },
          })),

        setAutoSavePending: (isPending) =>
          set((state) => ({
            autoSave: { ...state.autoSave, isPending },
          })),

        resetReportDetailStore: () => set(() => REPORT_DETAIL_INITIAL_STATE),
      },
    };
  });