{"id": "SnapReportsRealm", "realm": "SnapReportsRealm", "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": false, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": true, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": true, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 600, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 5184000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 3600, "clientSessionMaxLifespan": 86400, "clientOfflineSessionIdleTimeout": 5184000, "clientOfflineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "accountTheme": "keycloak", "loginTheme": "keycloak", "emailTheme": "keycloak", "adminTheme": "keycloak", "attributes": {"contentSecurityPolicy": "default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' data: https:; connect-src 'self' https://reportsbeta.snapforensics.com https://mature-worm-jointly.ngrok-free.app https://real-bee-suddenly.ngrok-free.app https://modest-shrew-urgently.ngrok-free.app https://supreme-macaw-picked.ngrok-free.app https://genuinely-larine-claire.ngrok-free.app; form-action 'self'; frame-ancestors 'self' https://reportsbeta.snapforensics.com https://mature-worm-jointly.ngrok-free.app https://real-bee-suddenly.ngrok-free.app https://modest-shrew-urgently.ngrok-free.app https://supreme-macaw-picked.ngrok-free.app https://genuinely-larine-claire.ngrok-free.app; frame-src 'self' https://reportsbeta.snapforensics.com https://mature-worm-jointly.ngrok-free.app https://real-bee-suddenly.ngrok-free.app https://modest-shrew-urgently.ngrok-free.app https://supreme-macaw-picked.ngrok-free.app https://genuinely-larine-claire.ngrok-free.app data:; child-src 'self' https://reportsbeta.snapforensics.com https://mature-worm-jointly.ngrok-free.app https://real-bee-suddenly.ngrok-free.app https://modest-shrew-urgently.ngrok-free.app https://supreme-macaw-picked.ngrok-free.app https://genuinely-larine-claire.ngrok-free.app data:; object-src 'none'; font-src 'self' data:;", "contentSecurityPolicyReportOnly": "", "checkLoginIframe": "false", "thirdPartyCookiesIframeEnabled": "false"}, "defaultRole": {"name": "standalone-role"}, "defaultDefaultClientScopes": ["openid", "roles", "web-origins", "permissions", "microprofile-jwt", "profile", "email"], "defaultGroups": ["UserStandAlone"], "clients": [{"clientId": "myclient", "name": "SnapReportsClient", "secret": "R9Z0KQyliyKtnUIY6Ipm1TJ+zDEfyQepmLGKZ6tw57o=", "enabled": true, "redirectUris": ["http://***************:8000/*", "http://***************:8000/*", "http://localhost", "http://localhost:8000/*", "https://mature-worm-jointly.ngrok-free.app/*", "http://***************:8000/auth/callback*", "https://mature-worm-jointly.ngrok-free.app/auth/callback*", "https://real-bee-suddenly.ngrok-free.app/auth/callback*", "https://modest-shrew-urgently.ngrok-free.app/auth/callback*", "https://supreme-macaw-picked.ngrok-free.app/auth/callback*", "https://genuinely-larine-claire.ngrok-free.app/auth/callback*", "http://localhost:8080/authkc/admin/master/console/*", "http://127.0.0.1:8080/authkc/admin/master/console/*", "http://localhost:8080/admin/master/console/*", "http://127.0.0.1:8080/admin/master/console/*", "http://*************/:8080/authkc/admin/master/console/*", "http://***************//:8080/authkc/admin/master/console/*", "https://mature-worm-jointly.ngrok-free.app/admin/master/console/*", "https://mature-worm-jointly.ngrok-free.app/authkc/admin/master/console/*", "https://real-bee-suddenly.ngrok-free.app/admin/master/console/*", "https://real-bee-suddenly.ngrok-free.app/authkc/admin/master/console/*", "https://modest-shrew-urgently.ngrok-free.app/admin/master/console/*", "https://modest-shrew-urgently.ngrok-free.app/authkc/admin/master/console/*", "https://supreme-macaw-picked.ngrok-free.app/admin/master/console/*", "https://supreme-macaw-picked.ngrok-free.app/authkc/admin/master/console/*", "https://genuinely-larine-claire.ngrok-free.app/admin/master/console/*", "https://genuinely-larine-claire.ngrok-free.app/authkc/admin/master/console/*"], "webOrigins": ["http://*************.102:8000", "http://***************:8000", "http://localhost", "https://mature-worm-jointly.ngrok-free.app/", "https://real-bee-suddenly.ngrok-free.app/", "https://modest-shrew-urgently.ngrok-free.app/", "https://supreme-macaw-picked.ngrok-free.app/", "https://genuinely-larine-claire.ngrok-free.app/"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String"}}, {"name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String"}}, {"name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}, {"name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"name": "User ID", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "sub", "jsonType.label": "String"}}], "optionalClientScopes": ["web-origins", "openid", "permissions", "profile", "roles", "microprofile-jwt", "email"], "access": {"view": true, "configure": true, "manage": true}}], "users": [{"username": "adminuser", "enabled": true, "emailVerified": true, "firstName": "Admin", "lastName": "User", "email": "<EMAIL>"}], "clientScopes": [{"name": "openid", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}}, {"name": "roles", "protocol": "openid-connect", "protocolMappers": [{"name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "config": {"claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true", "id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"name": "web-origins", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}}, {"name": "permissions", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}}, {"name": "microprofile-jwt", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}}, {"name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}, "protocolMappers": [{"name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"name": "audience", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "consentRequired": false, "config": {"included.client.audience": "myclient", "id.token.claim": "false", "access.token.claim": "true"}}]}, {"name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}, "protocolMappers": [{"name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-hardcoded-claim-mapper", "consentRequired": false, "config": {"claim.value": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}], "authenticationFlows": [{"alias": "browser", "description": "Browser flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "identity-provider-redirector", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false}]}, {"alias": "direct grant", "description": "OpenID Connect Direct Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false}]}, {"alias": "registration", "description": "Registration flow", "providerId": "form-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-registration-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": true}]}, {"alias": "reset credentials", "description": "Reset credentials flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-reset-cred-email", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false}]}, {"alias": "first-broker-login", "description": "First login with social providers", "providerId": "basic-flow", "topLevel": true, "builtIn": false, "authenticationExecutions": [{"authenticator": "idp-auto-link", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false}, {"authenticator": "idp-confirm-link", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false}, {"authenticator": "idp-review-profile", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false}]}, {"alias": "client registration", "description": "Client registration flow", "providerId": "form-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-client-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false}]}], "identityProviders": [{"alias": "google", "providerId": "google", "enabled": true, "updateProfileFirstLoginMode": "off", "trustEmail": true, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"clientId": "409707573351-30funda9lsmaml5is48nsdh0f6vjht9d.apps.googleusercontent.com", "clientSecret": "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", "useJwksUrl": "true", "defaultScope": "openid email profile", "trustEmail": "true", "linkOnly": "false"}}, {"alias": "microsoft", "providerId": "oidc", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"clientId": "****************************************", "clientSecret": "22b45c21-d4dd-46a1-9bd3-f4c9e045a1a6", "authorizationUrl": "https://login.microsoftonline.com/common/oauth2/v2.0/authorize", "tokenUrl": "https://login.microsoftonline.com/common/oauth2/v2.0/token", "logoutUrl": "https://login.microsoftonline.com/common/oauth2/v2.0/logout", "jwksUrl": "https://login.microsoftonline.com/common/discovery/v2.0/keys", "userInfoUrl": "https://graph.microsoft.com/oidc/userinfo", "defaultScope": "openid email profile", "trustEmail": "true", "linkOnly": "false"}}], "identityProviderMappers": [{"name": "google-picture-mapper", "identityProviderAlias": "google", "identityProviderMapper": "oidc-user-attribute-idp-mapper", "config": {"claim": "picture", "user.attribute": "picture", "syncMode": "INHERIT"}}, {"name": "google-email-mapper", "identityProviderAlias": "google", "identityProviderMapper": "oidc-user-attribute-idp-mapper", "config": {"claim": "email", "user.attribute": "email", "syncMode": "INHERIT"}}, {"name": "microsoft-picture-mapper", "identityProviderAlias": "microsoft", "identityProviderMapper": "oidc-user-attribute-idp-mapper", "config": {"claim": "picture", "user.attribute": "picture", "syncMode": "INHERIT"}}, {"name": "microsoft-email-mapper", "identityProviderAlias": "microsoft", "identityProviderMapper": "oidc-user-attribute-idp-mapper", "config": {"claim": "email", "user.attribute": "email", "syncMode": "INHERIT"}}], "roles": {"realm": [{"name": "administrador-role", "description": "Role for administrador", "composite": true, "clientRole": false, "composites": {"client": {"realm-management": ["query-clients", "view-clients"]}}}, {"name": "investigador-role", "description": "Role for investigador", "composite": true, "clientRole": false, "composites": {"client": {"realm-management": ["query-clients", "view-clients"]}}}, {"name": "role-edit-user", "description": "Edit user permission"}, {"name": "role-invite-user", "description": "Invite user permission"}, {"name": "role-add-api-key", "description": "Add API key permission"}, {"name": "role-create-report", "description": "Create report permission"}, {"name": "role-update-report", "description": "Update report permission"}, {"name": "role-view-report-list", "description": "View report list permission"}, {"name": "role-view-report-details", "description": "View report details permission"}, {"name": "role-get-user-logs", "description": "Get user logs permission"}, {"name": "role-get-organization-logs", "description": "Get organization logs permission"}, {"name": "role-answer-invite", "description": "Answer invite permission"}, {"name": "role-cancel-invite", "description": "Cancel invite permission"}, {"name": "role-get-organization-invite", "description": "Get organization invite permission"}, {"name": "role-get-user-invite", "description": "Get user invite permission"}, {"name": "role-get-data-from-user-invite", "description": "Get data from user invite permission"}, {"name": "role-get-invite-details", "description": "Get details from a invite sent"}, {"name": "role-leave-organization", "description": "Leave organization permission"}, {"name": "role-remove-organization", "description": "Remove user from organization permission"}, {"name": "role-create-folder", "description": "Create folder permission"}, {"name": "role-delete-folder", "description": "Delete folder permission"}, {"name": "role-rename-folder", "description": "Rename folder permission"}, {"name": "role-move-folder", "description": "Move folder permission"}, {"name": "role-merge-folder", "description": "Merge folder permission"}, {"name": "role-delete-report", "description": "Delete report permission"}, {"name": "role-print-snap-logo", "description": "Edit the view of logo snap permission"}, {"name": "role-view-folder-list", "description": "View the folders permission"}, {"name": "role-rename-report", "description": "Change the report name permission"}, {"name": "role-accept-terms", "description": "Accept the terms of use permission"}, {"name": "role-create-pdf", "description": "Create pdf permission"}, {"name": "role-get-eula", "description": "Get eula permission"}]}, "groups": [{"name": "UserStandAlone", "realmRoles": ["role-add-api-key", "role-create-report", "role-update-report", "role-view-report-list", "role-view-report-details", "role-get-user-logs", "role-answer-invite", "role-cancel-invite", "role-get-user-invite", "role-leave-organization", "standalone-role", "role-create-folder", "role-delete-folder", "role-rename-folder", "role-move-folder", "role-view-folder-list", "role-merge-folder", "role-delete-report", "role-rename-report", "role-accept-terms", "role-create-pdf", "role-get-eula"]}, {"name": "UserInvestigador", "realmRoles": ["role-create-report", "role-update-report", "role-view-report-list", "role-view-report-details", "role-get-user-logs", "role-answer-invite", "role-cancel-invite", "role-get-user-invite", "role-leave-organization", "investigador-role", "role-create-folder", "role-delete-folder", "role-rename-folder", "role-move-folder", "role-view-folder-list", "role-merge-folder", "role-delete-report", "role-rename-report", "role-accept-terms", "role-create-pdf", "role-get-eula"]}, {"name": "UserAdministrador", "realmRoles": ["role-edit-user", "role-invite-user", "role-add-api-key", "role-create-report", "role-update-report", "role-view-report-list", "role-view-report-details", "role-get-user-logs", "role-get-organization-logs", "role-answer-invite", "role-cancel-invite", "role-get-organization-invite", "role-get-user-invite", "role-get-data-from-user-invite", "role-leave-organization", "role-get-invite-details", "role-remove-organization", "administrador-role", "role-create-folder", "role-delete-folder", "role-rename-folder", "role-move-folder", "role-view-folder-list", "role-merge-folder", "role-delete-report", "role-print-snap-logo", "role-rename-report", "role-accept-terms", "role-create-pdf", "role-get-eula"]}]}