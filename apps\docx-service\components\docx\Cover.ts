import { ReportMetadata } from "../../global";
import { REPORT_CONSTANTS } from "../../config/constants";
import { LOGO_COMPLETA_HEADER } from "../../config/assets";
import { FONTS } from "../../config/fonts";
import { ImageRun, ISectionOptions, Paragraph, TextRun } from "docx";
import { TitlePage } from "./TitlePage";

interface ICoverProps {
  metadata: ReportMetadata;
  organization_logo?: string;
  should_print_snap_logo?: boolean;
}

export const Cover = ({
  metadata,
  organization_logo,
  should_print_snap_logo = true,
}: ICoverProps): ISectionOptions => {
  const reportName = metadata[
    REPORT_CONSTANTS.new_report.report_name
  ] as string;
  const modifiedAt = metadata[
    REPORT_CONSTANTS.new_report.modified_at
  ] as string;
  const reportSearchArgs = metadata[
    REPORT_CONSTANTS.new_report.report_search_args
  ] as Record<string, unknown>;

  const getSearchArguments = () => {
    if (!reportSearchArgs || typeof reportSearchArgs !== "object") {
      return [];
    }

    return Object.entries(reportSearchArgs)
      .filter(([key, value]) => {
        const lowerKey = key.toLowerCase();
        return value !== undefined &&
          value !== null &&
          value !== "" &&
          !lowerKey.includes("ignore")
      })
      .map(([key, value]) => ({
        key: key.toUpperCase().replace(/_[12]$/, ""),
        value: String(value),
      }));
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Não informado";
    try {
      return new Date(dateString).toLocaleDateString("pt-BR");
    } catch {
      return dateString;
    }
  };

  // const logoUrl = organization_logo ||

  const searchArgs = getSearchArguments();

  // Docx equivalents for the commented JSX structure
  const docxComponents = [
    // // Organization Logo
    // organization_logo &&
    //   new Paragraph({
    //     children: [
    //       new ImageRun({
    //         data: organization_logo, // Should be base64 or buffer
    //         transformation: { width: 100, height: 40 },
    //         type: "png",
    //       }),
    //     ],
    //   }),

    // // Snap Logo
    new Paragraph({
      children: [
        new ImageRun({
          data: LOGO_COMPLETA_HEADER,
          transformation: { width: 254, height: 40 },
          type: "png",
        }),
      ],
    }),

    // Espiral Image
    // new Paragraph({
    //   children: [
    //     new ImageRun({
    //       data: ESPIRAL_SRC,
    //       transformation: { width: 120, height: 20 },
    //       type: "png",
    //     }),
    //   ],
    // }),

    // Relatório Title
    new Paragraph({
      children: [
        new TextRun({
          text: "relatório",
          bold: true,
          size: 32,
          font: FONTS.MONO,
        }),
      ],
      spacing: { after: 200 },
    }),

    // Report Name Bar
    new Paragraph({
      children: [
        new TextRun({
          text: reportName,
          bold: true,
          size: 28,
        }),
      ],
      shading: { fill: "E5E5E5" },
      spacing: { after: 200 },
    }),

    // Data Section: Modified At
    new Paragraph({
      children: [
        new TextRun({
          text: "• modificado em: ",
          bold: true,
        }),
        new TextRun({
          text: formatDate(modifiedAt),
        }),
      ],
    }),

    // Data Section: Entradas
    new Paragraph({
      children: [
        new TextRun({
          text: "• Entradas: ",
          bold: true,
        }),
        ...(searchArgs.length > 0
          ? searchArgs.map(
            (arg) =>
              new TextRun({
                text: `${arg.key}: ${arg.value}`,
                bold: true,
              })
          )
          : [
            new TextRun({
              text: "CPF: 123.456.789-00",
            }),
          ]),
      ],
    }),

    // Description Texts
    new Paragraph({
      children: [
        new TextRun(
          "Este relatório contém informações pessoais tratadas a partir de fontes públicas e/ou de acesso autorizado, organizadas exclusivamente para fins de pesquisa, análise ou instrução de processos legais. Ainda que parte dos dados aqui apresentados seja de natureza pública, a sua compilação e estruturação configuram tratamento de dados pessoais, sujeito à legislação aplicável."
        ),
      ],
    }),
    new Paragraph({
      children: [
        new TextRun(
          "Nos termos da Lei nº 13.709/2018 (Lei Geral de Proteção de Dados - LGPD), o acesso a este documento é restrito a pessoas devidamente autorizadas, limitando-se à finalidade específica que motivou sua elaboração. O conteúdo não deve ser reproduzido, divulgado ou compartilhado, total ou parcialmente, sem base legal ou autorização expressa."
        ),
      ],
    }),

    // List Items
    new Paragraph({
      children: [new TextRun("O detentor deste material reconhece que:")],
    }),
    new Paragraph({
      children: [
        new TextRun(
          "1 - As informações são de caráter confidencial e devem ser utilizadas apenas dentro do escopo autorizado."
        ),
      ],
      bullet: { level: 0 },
    }),
    new Paragraph({
      children: [
        new TextRun(
          "2 - Qualquer uso indevido poderá implicar em responsabilidades civis, administrativas e/ou criminais."
        ),
      ],
      bullet: { level: 0 },
    }),
    new Paragraph({
      children: [
        new TextRun(
          "3 - A proteção da privacidade e da integridade dos dados apresentados é obrigação de todos que tenham acesso a este relatório."
        ),
      ],
      bullet: { level: 0 },
    }),

    new Paragraph({
      children: [
        new TextRun(
          "Este documento deve ser manuseado com o mais alto grau de sigilo, em conformidade com a LGPD e demais normas aplicáveis, garantindo a segurança da informação e o respeito aos direitos dos titulares de dados."
        ),
      ],
    }),

    // Footer
    new Paragraph({
      children: [
        new TextRun({
          text: "Uso restrito, conforme LGPD (Lei 13.709/2018). Compartilhamento ou cópia não autorizados é proibido.",
          italics: true,
          size: 20,
        }),
      ],
      alignment: "center",
      spacing: { before: 400 },
    }),
  ];

  return TitlePage([
    new Paragraph({
      children: [
        new TextRun({
          text: `modificado em: ${formatDate(modifiedAt)}`,
          bold: true,
        }),
        ...searchArgs.map(
          (arg, index) => new TextRun(`${arg.key}: ${arg.value}`)
        ),
        // create the jsx corresponding docx components above
        ...docxComponents,
      ],
    }),
  ]);
  // return (
  //   <View style={styles.container}>
  //     {/* Header Section */}
  //     <View style={styles.headerSection}>
  //       <View style={styles.headerContent}>
  //         {
  //           organization_logo && (
  //             <View style={styles.logoContainer}>
  //               <Image src={organization_logo} style={styles.logo} />
  //             </View>
  //           )
  //         }
  //       </View>
  //       {should_print_snap_logo && (
  //         <Image src={LOGO_COMPLETA_HEADER} style={styles.logoCompletaHeader} />
  //       )}
  //       <Image src={ESPIRAL_SRC} style={styles.grafismoBranco} />

  //       <View style={styles.relatorioContainer}>
  //         <Text style={styles.relatorioText}>relatório</Text>
  //       </View>

  //       <View style={styles.reportNameBar}>
  //         <Text style={styles.reportNameText}>{reportName}</Text>
  //       </View>
  //     </View>

  //     {/* Data Section */}
  //     <View style={styles.dataSection}>
  //       <View style={styles.dataContent}>
  //         <View style={styles.dataItem}>
  //           <View style={styles.labelContainer}>
  //             <View style={styles.bullet} />
  //             <Text style={styles.dataLabel}>modificado em</Text>
  //           </View>
  //           <Text style={styles.dataValue}>{formatDate(modifiedAt)}</Text>
  //         </View>

  //         <View style={styles.dataItem}>
  //           <View style={styles.labelContainer}>
  //             <View style={styles.bullet} />
  //             <Text style={styles.dataLabel}>Entradas</Text>
  //           </View>
  //           {searchArgs.length > 0 ? (
  //             searchArgs.map((arg, index) => (
  //               <Text key={index} style={styles.dataValue}>
  //                 <span style={{ fontWeight: 'bold' }}>{arg.key}</span>: {arg.value}
  //               </Text>
  //             ))
  //           ) : (
  //             <Text style={styles.dataValue}>CPF: 123.456.789-00</Text>
  //           )}
  //         </View>
  //       </View>
  //       <View style={styles.dataContent}>
  //         <Text style={styles.descriptionText}>
  //           Este relatório contém informações pessoais tratadas a partir de fontes públicas e/ou de acesso autorizado, organizadas exclusivamente para fins de pesquisa, análise ou instrução de processos legais. Ainda que parte dos dados aqui apresentados seja de natureza pública, a sua compilação e estruturação configuram tratamento de dados pessoais, sujeito à legislação aplicável.
  //         </Text>
  //         <Text style={styles.descriptionText}>
  //           Nos termos da Lei nº 13.709/2018 (Lei Geral de Proteção de Dados - LGPD), o acesso a este documento é restrito a pessoas devidamente autorizadas, limitando-se à finalidade específica que motivou sua elaboração. O conteúdo não deve ser reproduzido, divulgado ou compartilhado, total ou parcialmente, sem base legal ou autorização expressa.
  //         </Text>
  //         <View style={styles.list}>
  //           <Text style={styles.descriptionText}>
  //             O detentor deste material reconhece que:
  //           </Text>
  //           <Text style={styles.listItem}>
  //             1 - As informações são de caráter confidencial e devem ser utilizadas apenas dentro do escopo autorizado.
  //           </Text>
  //           <Text style={styles.listItem}>
  //             2 - Qualquer uso indevido poderá implicar em responsabilidades civis, administrativas e/ou criminais.
  //           </Text>
  //           <Text style={styles.listItem}>
  //             3 - A proteção da privacidade e da integridade dos dados apresentados é obrigação de todos que tenham acesso a este relatório.
  //           </Text>
  //         </View>
  //         <Text style={styles.descriptionText}>
  //           Este documento deve ser manuseado com o mais alto grau de sigilo, em conformidade com a LGPD e demais normas aplicáveis, garantindo a segurança da informação e o respeito aos direitos dos titulares de dados.
  //         </Text>
  //       </View>
  //     </View>

  //     <Image src={ESPIRAL_SRC} style={styles.grafismoCoral} />
  //     <View style={styles.footer}>
  //       <Text style={styles.footerText}>Uso restrito, conforme LGPD (Lei 13.709/2018). Compartilhamento ou cópia não autorizados é proibido.</Text>
  //     </View>
  //   </View>
  // );
};
