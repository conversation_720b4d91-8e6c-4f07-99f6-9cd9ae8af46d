import { ReportDataCombined, ReportSection } from "~/types/global";

/**
 * Helper function to extract sections from combinado format for trash view.
 * For combinado reports, we only show combined_data sections (not individual report sources).
 *
 * @param sections - Array of report sections (may include combinado container)
 * @returns Array of sections appropriate for trash view
 */
export const extractCombinadoSectionsForTrash = (sections: ReportSection[]): ReportSection[] => {
    const combinadoContainer = sections.find((section: any) =>
        section.combined_data || section.report_sources_data
    ) as ReportDataCombined | undefined;

    if (!combinadoContainer) return sections;

    if (Array.isArray(combinadoContainer.combined_data)) {
        return combinadoContainer.combined_data;
    }

    return sections;
};