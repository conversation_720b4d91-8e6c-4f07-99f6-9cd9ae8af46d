{"name": "puppeteer-service", "version": "1.0.0", "description": "Puppeteer service for PDF generation", "main": "dist/main.js", "scripts": {"start": "node dist/main.js", "build": "tsc && mkdir -p dist/assets && copyfiles -u 1 'assets/**/*' dist/assets && ls -la dist/assets/", "dev": "tsc -w"}, "dependencies": {"express": "^5.1.0", "puppeteer": "^24.11.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "kafkajs": "^2.2.4", "minio": "^8.0.1", "pdf-lib": "^1.17.1"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/node": "^24.0.10", "typescript": "^5.8.3", "@types/cors": "^2.8.17", "@types/express": "^5.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/minio": "^7.1.1", "copyfiles": "^2.4.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0"}, "pnpm": {"overrides": {"tar-fs": "3.1.1"}}}