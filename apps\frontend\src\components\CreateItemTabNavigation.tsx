import { Button } from "@snap/design-system";

interface CreateItemTabNavigationProps {
  activeTab: "report" | "folder";
  onReportTabClick: () => void;
  onFolderTabClick: () => void;
}

export function CreateItemTabNavigation({
  activeTab,
  onReportTabClick,
  onFolderTabClick
}: CreateItemTabNavigationProps) {
  return (
    <div className="flex items-center">
      <Button
        className={`uppercase w-full !rounded-none ${activeTab === "report" && "!bg-foreground !text-background"}`}
        onClick={onReportTabClick}
        data-testid="button-create-report"
      >
        Criar Relatório
      </Button>
      <Button
        className={`uppercase w-full !rounded-none ${activeTab === "folder" && "!bg-foreground !text-background"}`}
        onClick={onFolderTabClick}
        data-testid="button-create-folder"
      >
        <PERSON><PERSON><PERSON>
      </Button>
    </div>
  );
}
