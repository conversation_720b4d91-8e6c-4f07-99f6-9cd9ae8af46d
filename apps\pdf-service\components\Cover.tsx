import React from 'react';
import { View, Text, Image, StyleSheet } from './pdf-components';
import { ReportMetadata } from '../global';
import { REPORT_CONSTANTS } from '../config/constants';
import { ESPIRAL_SRC, LOGO_COMPLETA_HEADER } from '../config/assets';
import { FONTS } from '../config/fonts';

interface CoverProps {
  metadata: ReportMetadata;
  organization_logo?: string;
  should_print_snap_logo?: boolean;
}

export const Cover: React.FC<CoverProps> = ({ metadata, organization_logo, should_print_snap_logo = true }) => {
  const reportName = metadata[REPORT_CONSTANTS.new_report.report_name] as string;
  const modifiedAt = metadata[REPORT_CONSTANTS.new_report.modified_at] as string;
  const reportSearchArgs = metadata[REPORT_CONSTANTS.new_report.report_search_args] as Record<string, unknown>;

  const getSearchArguments = () => {
    if (!reportSearchArgs || typeof reportSearchArgs !== 'object') {
      return [];
    }

    return Object.entries(reportSearchArgs)
      .filter(([key, value]) => {
        const lowerKey = key.toLowerCase();
        return value !== undefined &&
          value !== null &&
          value !== '' &&
          !lowerKey.includes("ignore")
      })
      .map(([key, value]) => ({
        key: key.toUpperCase().replace(/_[12]$/, ''),
        value: String(value)
      }));
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Não informado';
    try {
      return new Date(dateString).toLocaleDateString('pt-BR');
    } catch {
      return dateString;
    }
  };

  const searchArgs = getSearchArguments();

  return (
    <View style={styles.container}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        <View style={styles.headerContent}>
          {
            organization_logo && (
              <View style={styles.logoContainer}>
                <Image src={organization_logo} style={styles.logo} />
              </View>
            )
          }
        </View>
        {should_print_snap_logo && (
          <Image src={LOGO_COMPLETA_HEADER} style={styles.logoCompletaHeader} />
        )}
        <Image src={ESPIRAL_SRC} style={styles.grafismoBranco} />

        <View style={styles.relatorioContainer}>
          <Text style={styles.relatorioText}>relatório</Text>
        </View>

        <View style={styles.reportNameBar}>
          <Text style={styles.reportNameText}>{reportName}</Text>
        </View>
      </View>

      {/* Data Section */}
      <View style={styles.dataSection}>
        <View style={styles.dataContent}>
          <View style={styles.dataItem}>
            <View style={styles.labelContainer}>
              <View style={styles.bullet} />
              <Text style={styles.dataLabel}>modificado em</Text>
            </View>
            <Text style={styles.dataValue}>{formatDate(modifiedAt)}</Text>
          </View>

          <View style={styles.dataItem}>
            <View style={styles.labelContainer}>
              <View style={styles.bullet} />
              <Text style={styles.dataLabel}>Entradas</Text>
            </View>
            {searchArgs.length > 0 ? (
              searchArgs.map((arg, index) => (
                <Text key={index} style={styles.dataValue}>
                  <span style={{ fontWeight: 'bold' }}>{arg.key}</span>: {arg.value}
                </Text>
              ))
            ) : (
              <Text style={styles.dataValue}>CPF: 123.456.789-00</Text>
            )}
          </View>
        </View>
        <View style={styles.dataContent}>
          <Text style={styles.descriptionText}>
            Este relatório contém informações pessoais tratadas a partir de fontes públicas e/ou de acesso autorizado, organizadas exclusivamente para fins de pesquisa, análise ou instrução de processos legais. Ainda que parte dos dados aqui apresentados seja de natureza pública, a sua compilação e estruturação configuram tratamento de dados pessoais, sujeito à legislação aplicável.
          </Text>
          <Text style={styles.descriptionText}>
            Nos termos da Lei nº 13.709/2018 (Lei Geral de Proteção de Dados - LGPD), o acesso a este documento é restrito a pessoas devidamente autorizadas, limitando-se à finalidade específica que motivou sua elaboração. O conteúdo não deve ser reproduzido, divulgado ou compartilhado, total ou parcialmente, sem base legal ou autorização expressa.
          </Text>
          <View style={styles.list}>
            <Text style={styles.descriptionText}>
              O detentor deste material reconhece que:
            </Text>
            <Text style={styles.listItem}>
              1 - As informações são de caráter confidencial e devem ser utilizadas apenas dentro do escopo autorizado.
            </Text>
            <Text style={styles.listItem}>
              2 - Qualquer uso indevido poderá implicar em responsabilidades civis, administrativas e/ou criminais.
            </Text>
            <Text style={styles.listItem}>
              3 - A proteção da privacidade e da integridade dos dados apresentados é obrigação de todos que tenham acesso a este relatório.
            </Text>
          </View>
          <Text style={styles.descriptionText}>
            Este documento deve ser manuseado com o mais alto grau de sigilo, em conformidade com a LGPD e demais normas aplicáveis, garantindo a segurança da informação e o respeito aos direitos dos titulares de dados.
          </Text>
        </View>
      </View>

      <Image src={ESPIRAL_SRC} style={styles.grafismoCoral} />
      <View style={styles.footer}>
        <Text style={styles.footerText}>Uso restrito, conforme LGPD (Lei 13.709/2018). Compartilhamento ou cópia não autorizados é proibido.</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    minHeight: '100vh',
    maxHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    pageBreakAfter: 'avoid',
  },
  headerSection: {
    position: 'relative',
    height: 400,
    padding: 32,
    overflow: 'hidden',
    flexShrink: 0,
  },
  headerContent: {
    height: '100%',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
    maxWidth: 200
  },
  logo: {
    width: '100%',
    height: 'auto',
  },
  icon: {
    width: 36,
    height: 36,
  },
  grafismoBranco: {
    position: 'absolute',
    top: -170,
    right: -400,
    zIndex: 10,
    mixBlendMode: 'multiply',
    opacity: 0.3,
  },
  logoCompletaHeader: {
    position: 'absolute',
    top: 32,
    right: 32,
    width: 244,
    zIndex: 10,
  },
  relatorioContainer: {
    position: 'absolute',
    bottom: 40,
    left: 32,
    width: '100%',
  },
  relatorioText: {
    fontFamily: FONTS.MONO,
    fontSize: 30,
    textTransform: 'uppercase',
    color: '#000',
  },
  reportNameBar: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: '100%',
    height: 32,
    backgroundColor: '#FE473C',
    paddingHorizontal: 32,
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportNameText: {
    color: '#FFFFFF',
    fontFamily: FONTS.MONO,
    textTransform: 'uppercase',
    fontWeight: 'bold',
    fontSize: 18,
  },
  dataSection: {
    backgroundColor: '#FFFFFF',
    padding: 32,
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'relative',
    flex: 1,
    overflow: 'hidden',
  },
  dataContent: {
    flexDirection: 'column',
    gap: 32,
    maxWidth: '45%',
  },
  dataItem: {
    flexDirection: 'column',
    gap: 6,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bullet: {
    width: 8,
    height: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  dataLabel: {
    textTransform: 'uppercase',
    fontFamily: FONTS.MONO,
    color: 'rgba(0, 0, 0, 0.8)',
    fontSize: 12,
  },
  dataValue: {
    textTransform: 'uppercase',
    color: '#000',
    fontSize: 14,
  },
  grafismoCoral: {
    position: 'fixed',
    bottom: -170,
    left: -340,
    width: 600,
    height: 'auto',
    zIndex: 10,
    mixBlendMode: 'multiply',
    opacity: 0.3,
    transform: 'rotate(190deg)',
  },
  descriptionText: {
    fontSize: 12,
    lineHeight: 1.2,
    color: '#000',
  },
  list: {
    flexDirection: 'column',
    gap: 16,
  },
  listItem: {
    fontSize: 12,
    lineHeight: 1.2,
    color: '#000',
    marginLeft: 16,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 12,
    overflow: 'hidden',
    pageBreakInside: 'avoid',
  },
  footerText: {
    color: '#000',
    fontSize: 11,
    fontWeight: 'bold',
  },
});

export default Cover;