import { REPORT_CONSTANTS } from "./constants";

export interface CreateReportFormData {
  selectedReportType: string;
  reportInputValue: string;
  document1Type: string;
  document1Value: string;
  document2Type: string;
  document2Value: string;
  reportMode: "individual" | "relacoes";
  ignoreConsortiums: boolean;
  ignoreOtherContacts: boolean;
}

/**
 * Validates if the report form data is complete and valid
 */
export function validateReportForm(data: CreateReportFormData): boolean {
  if (data.reportMode === "relacoes") {
    return !!(data.document1Type && data.document1Value && data.document2Type && data.document2Value);
  }
  return !!(data.selectedReportType && data.reportInputValue);
}

/**
 * Formats the search arguments for the report creation
 */
export function formatSearchArgs(data: CreateReportFormData): Record<string, any> {
  const baseArgs = data.reportMode === "relacoes"
    ? {
      [`${data.document1Type}_1`]: data.document1Value,
      [`${data.document2Type}_2`]: data.document2Value,
    }
    : {
      [data.selectedReportType]: [data.reportInputValue],
    };

  // Add ignore flags for relações reports
  if (data.reportMode === "relacoes") {
    return {
      ...baseArgs,
      ignore_consortiums: data.ignoreConsortiums,
      ignore_other_contacts: data.ignoreOtherContacts,
    };
  }

  return baseArgs;
}

/**
 * Gets the plain text value for report creation
 */
export function getPlainTextValue(data: CreateReportFormData): string | Record<string, any> {
  if (data.reportMode === "relacoes") {
    return formatSearchArgs(data);
  }
  return data.reportInputValue;
}

/**
 * Checks if the report type is relações
 */
export function isRelacoesReport(reportType: string): boolean {
  return reportType === REPORT_CONSTANTS.types.relacoes;
}

/**
 * Validates email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Checks if email validation is needed for the report type
 */
export function shouldValidateEmail(reportType: string, inputValue: string): boolean {
  return reportType === "email" && inputValue.trim() !== "";
}
