import { useCallback, useEffect, useMemo } from "react";
import { toast } from "sonner";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { handleQueryError } from "~/helpers/errorHandling.helper";
import { useReportDetailStore } from "~/store/details/store";
import { REPORT_DETAIL_STORE_INSTANCE, UpdateFnPromise } from "~/store/details";
import { usePasswordCheck } from "~/hooks/usePasswordCheck";

let globalForceSave: (() => void) | null = null;

export const getGlobalForceSave = () => globalForceSave;

export function useReportActionsWithAutoSave() {
  const { withPasswordCheck } = usePasswordCheck();
  const actions = useReportDetailStore((state) => state.actions);
  const { addNewReportMutation,
    setReportDetailsToStale,
    resetReportListQuery
  } = useReportCRUD();

  const executeSave = useCallback(() => {
    const state = useReportDetailStore.getState();

    if (!state.autoSave.hasPendingChanges || state.autoSave.isPending) {
      return;
    }

    const { sections, metadata } = state.data;
    const reportId = metadata?.user_reports_id as string;

    const payload = {
      ...metadata,
      data: { [metadata?.report_type as string]: sections },
    };

    console.log("[useReportActionsWithAutoSave] Executando save com estado final:", payload);

    useReportDetailStore.setState((state) => ({
      ...state,
      autoSave: {
        ...state.autoSave,
        isPending: true,
        hasPendingChanges: false,
        mutation: addNewReportMutation,
      }
    }));

    addNewReportMutation.mutate(payload, {
      onSuccess: () => {
        useReportDetailStore.setState((state) => ({
          ...state,
          autoSave: {
            ...state.autoSave,
            isPending: false,
          }
        }));
        toast.success("Alterações salvas com sucesso!");
        withPasswordCheck(() => {
          if (reportId) {
            setReportDetailsToStale(reportId)
            resetReportListQuery() // TODO - por quanto estou fazendo refetch da lista para mostrar o card do report modificado primeiro - REVISAR
          };
        });
      },
      onError: (error) => {
        useReportDetailStore.setState((state) => ({
          ...state,
          autoSave: {
            ...state.autoSave,
            isPending: false,
            hasPendingChanges: false,
          }
        }));

        handleQueryError(error, {
          serverErrorMessage: "Falha ao salvar, tente novamente mais tarde.",
          title: "Erro ao salvar",
          onError: () => {
            if (reportId) {
              actions.forceReload();
            }
          }
        });
      },
    });
  }, [addNewReportMutation, actions, withPasswordCheck, setReportDetailsToStale, resetReportListQuery]);

  useEffect(() => {
    globalForceSave = executeSave;
    return () => {
      globalForceSave = null;
    };
  }, [executeSave]);

  const updateSectionEntries = useCallback((
    sectionTitle: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: any) => boolean,
    testSectionDeletedFn: (section: any) => boolean,
    calculateDataCountFn?: (section: any) => number,
    includeSubsections?: boolean,
    crossSectionUpdate?: { matchingProp: string; updaterFn: UpdateFnPromise }
  ) => {
    REPORT_DETAIL_STORE_INSTANCE.events.updateSectionEntries(
      sectionTitle,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn,
      includeSubsections,
      crossSectionUpdate
    );

    REPORT_DETAIL_STORE_INSTANCE.events.scheduleAutoSave();
  }, []);

  const updateSubsectionWithMainSection = useCallback((
    sectionTitle: string,
    subsectionName: string,
    matchingProp: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: any) => boolean,
    testSectionDeletedFn: (section: any) => boolean,
    calculateDataCountFn?: (section: any) => number
  ) => {
    REPORT_DETAIL_STORE_INSTANCE.events.updateSubsectionWithMainSection(
      sectionTitle,
      subsectionName,
      matchingProp,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn
    );

    REPORT_DETAIL_STORE_INSTANCE.events.scheduleAutoSave();
  }, []);

  return useMemo(() => ({
    ...actions,
    updateSectionEntries,
    updateSubsectionWithMainSection,
    forceSave: executeSave,
  }), [actions, updateSectionEntries, updateSubsectionWithMainSection, executeSave]);
}

