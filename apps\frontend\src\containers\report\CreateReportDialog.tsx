import { Button } from "@snap/design-system";
import { Check } from "lucide-react";
import { useEffect, useCallback, useMemo } from "react";
import { useUserData } from "~/store/userStore";
import {
  useActiveTab,
  useFolderName,
  useSelectedReports,
  useCreateFolderActions,
  useCanCreateFolder,
} from "~/store/createFolderStore";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { toast } from "sonner";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { useDialogActions } from "~/store/dialogStore";
import { translatePropToLabel } from "~/helpers";
import { useCreateReportForm } from "~/hooks/useCreateReportForm";
import { ReportModeSelector } from "~/components/ReportModeSelector";
import { IndividualReportForm } from "~/components/IndividualReportForm";
import { RelationsReportForm } from "~/components/RelationsReportForm";
import { FolderCreationForm } from "~/components/FolderCreationForm";
import { CreateItemTabNavigation } from "~/components/CreateItemTabNavigation";
import { useNewReportActions } from "~/store/newReportStore";

export function CreateReportDialogContent() {
  const userData = useUserData();
  const {
    formData,
    setSelectedReportType,
    setReportInputValue,
    setDocument1Type,
    setDocument1Value,
    setDocument2Type,
    setDocument2Value,
    setReportMode,
    setIgnoreConsortiums,
    setIgnoreOtherContacts
  } = useCreateReportForm();
  /* STORE */
  const activeTab = useActiveTab();
  const folderName = useFolderName();
  const { setActiveTab, setFolderName } = useCreateFolderActions();
  const reportTypes = userData?.report_types;
  const reportTypeKeys = {
    cpf: REPORT_CONSTANTS.types.cpf,
    cnpj: REPORT_CONSTANTS.types.cnpj,
    email: REPORT_CONSTANTS.types.email,
    telefone: REPORT_CONSTANTS.types.telefone,
    relacoes: REPORT_CONSTANTS.types.relacoes,
    combinado: REPORT_CONSTANTS.types.combinado
  }
  const reportTypeOptions = useMemo(() => {
    if (!reportTypes) return [];

    const filteredTypes = reportTypes.filter((type) => {
      if (type === reportTypeKeys.combinado) return false;
      if (formData.reportMode === "individual" && type === reportTypeKeys.relacoes) return false;
      if (formData.reportMode === reportTypeKeys.relacoes && type !== reportTypeKeys.relacoes) return false;
      return true;
    });

    return filteredTypes.map((type) => ({
      value: type,
      label: translatePropToLabel(type).toUpperCase(),
    }));
  }, [reportTypes, formData.reportMode]);

  const documentTypeOptions = useMemo(() => [
    { value: "cpf", label: "CPF" },
    { value: "cnpj", label: "CNPJ" }
  ], []);

  useEffect(() => {
    if (formData.reportMode === reportTypeKeys.relacoes) {
      setSelectedReportType(reportTypeKeys.relacoes);
      if (!formData.document1Type) setDocument1Type(reportTypeKeys.cpf);
      if (!formData.document2Type) setDocument2Type(reportTypeKeys.cpf);
    } else {
      const firstType = reportTypes?.filter(type => type !== "combinado" && type !== "relacoes")[0] || "";
      setSelectedReportType(firstType);
    }
  }, [userData?.report_types, formData.reportMode]);

  const handleReportTabClick = useCallback(() => {
    setActiveTab("report");
  }, [setActiveTab]);

  const handleFolderTabClick = useCallback(() => {
    setActiveTab("folder");
  }, [setActiveTab]);

  const handleFolderNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFolderName(e.target.value);
  }, [setFolderName]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (activeTab === 'report') {
        const isValid = formData.reportMode === "relacoes"
          ? formData.document1Type && formData.document1Value && formData.document2Type && formData.document2Value
          : formData.selectedReportType && formData.reportInputValue;

        if (isValid) {
          const createButton = document.querySelector('[data-testid="button-confirm-create-report"]') as HTMLButtonElement;
          createButton?.click();
        }
      } else if (activeTab === 'folder') {
        if (folderName.trim()) {
          const createButton = document.querySelector('[data-testid="button-confirm-create-folder"]') as HTMLButtonElement;
          createButton?.click();
        }
      }
    }
  }, [activeTab, formData, folderName]);


  return (
    <div className="space-y-4 min-h-[200px]">
      <p className="text-md mb-4 font-semibold">
        Que tipo de item você deseja criar?
      </p>
      <CreateItemTabNavigation
        activeTab={activeTab}
        onReportTabClick={handleReportTabClick}
        onFolderTabClick={handleFolderTabClick}
      />
      <div className="mb-12">
        {activeTab === "report" && (
          <div className="space-y-4">
            <ReportModeSelector
              reportMode={formData.reportMode}
              onReportModeChange={setReportMode}
            />

            {formData.reportMode === "individual" && (
              <IndividualReportForm
                reportTypeOptions={reportTypeOptions}
                selectedReportType={formData.selectedReportType}
                reportInputValue={formData.reportInputValue}
                onReportTypeChange={setSelectedReportType}
                onInputValueChange={setReportInputValue}
                onKeyDown={handleKeyDown}
                hasReportTypes={!!userData?.report_types?.length}
              />
            )}

            {formData.reportMode === "relacoes" && (
              <RelationsReportForm
                documentTypeOptions={documentTypeOptions}
                document1Type={formData.document1Type}
                document1Value={formData.document1Value}
                document2Type={formData.document2Type}
                document2Value={formData.document2Value}
                ignoreOtherContacts={formData.ignoreOtherContacts}
                ignoreConsortiums={formData.ignoreConsortiums}
                selectedReportType={formData.selectedReportType}
                onDocument1TypeChange={setDocument1Type}
                onDocument1ValueChange={setDocument1Value}
                onDocument2TypeChange={setDocument2Type}
                onDocument2ValueChange={setDocument2Value}
                onIgnoreOtherContactsChange={setIgnoreOtherContacts}
                onIgnoreConsortiumsChange={setIgnoreConsortiums}
                onKeyDown={handleKeyDown}
              />
            )}
          </div>
        )}

        {activeTab === "folder" && (
          <FolderCreationForm
            folderName={folderName}
            onFolderNameChange={handleFolderNameChange}
            onKeyDown={handleKeyDown}
          />
        )}
      </div>
    </div>
  );
}

export function CreateReportDialogFooter() {
  const { createFolderMutation } = useFolderCRUD();
  const { closeDialog } = useDialogActions();
  const {
    isFormValid,
    isLoading,
    handleCreateReport
  } = useCreateReportForm();
  /* STORE */
  const activeTab = useActiveTab();
  const folderName = useFolderName();
  const selectedReports = useSelectedReports();
  const canCreateFolder = useCanCreateFolder();
  const { resetStore } = useCreateFolderActions();
  const { clearNewReportValues } = useNewReportActions();

  const handleCreateFolder = () => {
    const trimmedFolderName = folderName.trim();
    if (!trimmedFolderName) {
      toast("Erro", {
        description: "Nome da pasta é obrigatório",
        action: {
          label: "Fechar",
          onClick: () => { },
        },
      });
      return;
    }

    const limitedFolderName = trimmedFolderName.substring(0, 100);
    createFolderMutation.mutate({
      folderName: limitedFolderName,
      selectedReports,
    });
  };

  const handleCancel = () => {
    clearNewReportValues();
    resetStore();
    closeDialog();
  };

  const isReportValid = isFormValid;

  return (
    <div className="flex gap-4">
      {activeTab === "report" && (
        <Button
          className="uppercase"
          onClick={handleCreateReport}
          data-testid="button-confirm-create-report"
          disabled={isLoading || !isReportValid}
          iconPosition="right"
          icon={isLoading ? <AiOutlineLoading3Quarters size={20} className="animate-spin" /> : <Check />}
        >
          Criar Relatório
        </Button>
      )}

      {activeTab === "folder" && (
        <Button
          className="uppercase"
          onClick={handleCreateFolder}
          data-testid="button-confirm-create-folder"
          disabled={!canCreateFolder || createFolderMutation.isPending}
          iconPosition="right"
          icon={createFolderMutation.isPending ? <AiOutlineLoading3Quarters size={20} className="animate-spin" /> : <Check />}
        >
          {createFolderMutation.isPending ? "Criando..." : "Criar Pasta"}
        </Button>
      )}

      <Button
        onClick={handleCancel}
        data-testid="button-cancel-create-report"
      >
        Cancelar
      </Button>
    </div>
  );
}

export const CreateReportDialog = {
  Content: CreateReportDialogContent,
  Footer: CreateReportDialogFooter,
};
