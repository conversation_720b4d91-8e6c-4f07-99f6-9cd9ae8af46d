import React, { useState, useEffect, useMemo } from 'react';
import { useParams } from 'react-router';
import { Avatar, ChamferBox, Text } from '@snap/design-system';
import { getInitials, maskDocumentNumber, getAgeFromBirthDate, isReportSubjectCompany } from '../../helpers';
import { REPORT_CONSTANTS } from '../../config/constants';
import { isValidUrl } from '../strategies/helpers.strategy';
import { cn } from './utils';
import { useReportMetadata, useReportType, useProfileImage } from '../../context/ReportContext';
import { ReportsCustomLabel } from './ReportsCustomLabel';
import { FaUsers } from 'react-icons/fa6';
import { TbBuildings } from 'react-icons/tb';
import { TbSwitch3 } from "react-icons/tb";

const REPORT_TYPE_CONFIG = {
  [REPORT_CONSTANTS.types.relacoes]: {
    icon: <TbSwitch3 className="size-20" />,
    title: "Relatório de Relações",
    showAvatar: false,
  },
  multiple: {
    icon: (personCount: number) => personCount ? <FaUsers className="size-16" /> : <TbBuildings className="size-16" />,
    showAvatar: false,
  },
  default: {
    showAvatar: true,
  }
} as const;

const ReportProfileHeader: React.FC = () => {
  const metadata = useReportMetadata();
  const reportType = useReportType();
  const image = useProfileImage();
  const { id: currentReportId, type: currentReportType } = useParams<{ type: string; id: string }>();
  const [isAvatarLoading, setIsAvatarLoading] = useState(false);

  /* metadados  */
  const profileName = (metadata[REPORT_CONSTANTS.new_report.subject_name] as string);
  const initials = getInitials(profileName);
  const ageFromMeta = metadata[REPORT_CONSTANTS.new_report.subject_age] as string | number | undefined;
  const idade = typeof ageFromMeta === 'number' ? ageFromMeta : getAgeFromBirthDate(metadata[REPORT_CONSTANTS.new_report.subject_age] as string);
  const data_fundacao = new Date(ageFromMeta ? ageFromMeta as string : '').toLocaleDateString('pt-BR');
  const sexo = (metadata[REPORT_CONSTANTS.new_report.subject_sex] as string);
  const nomeMae = (metadata[REPORT_CONSTANTS.new_report.subject_mother_name] as string);
  const companyCount = metadata[REPORT_CONSTANTS.new_report.subject_company_count];
  const personCount = metadata[REPORT_CONSTANTS.new_report.subject_person_count];
  const reportSearchArgs = metadata[REPORT_CONSTANTS.new_report.report_search_args];
  const numberOfRelations = metadata[REPORT_CONSTANTS.new_report.number_of_relations] as number || 0;
  const isCompany = isReportSubjectCompany(reportType, ageFromMeta, sexo, nomeMae);
  const isMultipleSubjects = profileName === REPORT_CONSTANTS.multiplos_registros_encontrados;
  const isRelacoes = reportType === REPORT_CONSTANTS.types.relacoes;
  const isCombinado = reportType === REPORT_CONSTANTS.types.combinado;
  const reportSourcesUsed = metadata[REPORT_CONSTANTS.new_report.report_sources_used];
  const numberOfCombinedReports = Array.isArray(reportSourcesUsed) ? reportSourcesUsed.length : 0;
  const showCombinedReportsBagde = isCombinado && numberOfCombinedReports > 2;

  const getCurrentConfig = () => {
    if (isRelacoes) return REPORT_TYPE_CONFIG[REPORT_CONSTANTS.types.relacoes];
    if (isMultipleSubjects) return REPORT_TYPE_CONFIG.multiple;
    return REPORT_TYPE_CONFIG.default;
  };

  const currentConfig = getCurrentConfig();
  const ageLabel = isCompany ? 'DATA DE FUNDAÇÃO:' : 'IDADE:';
  const ageValue = isCompany ? data_fundacao : idade;

  const getReportProperties = () => {
    const commonProps = [
      { label: 'PESSOAS ENCONTRADAS', value: personCount },
      { label: 'EMPRESAS ENCONTRADAS', value: companyCount },
    ];

    const specificProps = isCompany
      ? [
        { label: 'STATUS NA RECEITA:', value: 'ATIVO' }, // TODO - mockado
        { label: ageLabel, value: ageValue },
      ]
      : [
        { label: ageLabel, value: ageValue },
        { label: 'SEXO:', value: sexo },
        { label: 'NOME DA MÃE:', value: nomeMae },
      ];

    return [...commonProps, ...specificProps].filter(prop => isValidValue(prop.value));
  };

  const isValidValue = (val: any) => val !== undefined && val !== null && val !== '' && val !== 0;
  
  const searchArguments = useMemo((): Array<{ key: string; value: string }> => {
    if (!reportSearchArgs || typeof reportSearchArgs !== 'object' || !metadata) {
      return [];
    }

    const metadataReportId = metadata[REPORT_CONSTANTS.new_report.report_id];
    const metadataReportType = metadata[REPORT_CONSTANTS.new_report.report_type];

    if (currentReportId && currentReportType) {
      if (metadataReportType !== currentReportType) {
        return [];
      }

      if (!metadataReportId || (typeof metadataReportId === 'object' && Object.keys(metadataReportId).length === 0)) {
        return [];
      }
    }

    const validSearchArgsEntries = Object.entries(reportSearchArgs).filter(([key]) => !key.includes("ignore"));
    return validSearchArgsEntries
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .map(([key, value]) => ({
        key: key.toUpperCase().replace(/_[12]$/, ''),
        value: maskDocumentNumber(value as string, key)
      }));
  }, [reportSearchArgs, metadata, currentReportId, currentReportType]);

  const renderAvatar = () => {
    if (!currentConfig.showAvatar) {
      const icon = isMultipleSubjects
        ? (REPORT_TYPE_CONFIG.multiple.icon as Function)(personCount)
        : currentConfig.icon;

      return (
        <div className="flex items-center justify-center h-full w-full">
          <div className="border-12 border-border rounded-full p-6 flex items-center justify-center h-46 w-46">
            {icon}
          </div>
        </div>
      );
    }

    return (
      <>
        {isAvatarLoading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center h-full">
            <div className="animate-spin h-20 w-20 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        )}
        <div className={`${isAvatarLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-1000 ease-in-out aspect-square`}>
          <Avatar
            size="22"
            className={cn(
              "border-12 border-border w-full h-full",
              "[&_img]:h-full [&_img]:w-auto [&_img]:object-cover",
              "[&_span]:h-40 [&_span]:w-40 [&_span]:text-6xl"
            )}
            src={isValidUrl(image || '') ? image : undefined}
            fallback={initials}
            textAlign="left"
          />
        </div>
      </>
    );
  };

  const renderContent = () => {
    if (isRelacoes) {
      return (
        <div>
          <Text variant="body-xlg" className='leading-relaxed'>
            Foram encontrados
            <span className='font-bold'>{` ${numberOfRelations} registros `}</span>
            na relação
          </Text>
          <Text variant="body-xlg" className='leading-relaxed'>
            entre as duas entradas informadas.
          </Text>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 gap-4">
        {getReportProperties().map((prop) => (
          <div key={prop.label}>
            <ReportsCustomLabel label={prop.label} />
            <Text variant="body-md">{prop.value as string}</Text>
          </div>
        ))}
      </div>
    );
  };

  const getTitle = () => {
    if (isRelacoes && 'title' in currentConfig) return currentConfig.title;
    return profileName;
  };

  useEffect(() => {
    setIsAvatarLoading(true);
    if (!image) {
      setIsAvatarLoading(false);
      return;
    }
    const img = new Image();
    img.src = image;
    img.onload = () => setIsAvatarLoading(false);
    img.onerror = () => setIsAvatarLoading(false);
    const timer = setTimeout(() => setIsAvatarLoading(false), 5000);
    return () => clearTimeout(timer);
  }, [image]);

  return (
    <ChamferBox corner="bottomRight" className="!border-border-alt w-full rounded-xl [--border:var(--border-alt)] p-4">
      <div className="flex gap-6">
        <div className="relative h-46 w-46 aspect-square">
          {renderAvatar()}
        </div>

        <div className="flex-2/4 space-y-8">
          <h1 className="text-3xl font-bold">{getTitle()}</h1>
          {renderContent()}
        </div>

        <div className="flex-1/5 pl-4 space-y-4">
          <h2 className="text-lg font-mono">ENTRADAS</h2>
          {searchArguments?.map(({ key, value }) => (
            <div key={key + value}>
              <ReportsCustomLabel label={key} colorClass="bg-primary" />
              <Text>{value}</Text>
            </div>
          ))}
          {
            showCombinedReportsBagde ?
              <div className="flex items-center justify-center rounded-full p-2 bg-border h-[44px] w-[44px] overflow-hidden" title={`+${numberOfCombinedReports} relatórios`}>
                <Text variant="body-md" align="center" className="font-bold truncate w-full text-center">
                  +{numberOfCombinedReports}
                </Text>
              </div>
              : null
          }
        </div>
      </div>
    </ChamferBox>
  );
};

export default ReportProfileHeader;