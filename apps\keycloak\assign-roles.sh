#!/bin/bash
set -e

# --- Configuration Variables ---
REALM="${KC_REALM:-SnapReportsRealm}"
CLIENT_ID="myclient"
SERVICE_USER="service-account-$CLIENT_ID"
# ROLES=("manage-users" "view-users" "query-groups" "query-users")
ROLES=("manage-users" "view-users" "query-groups" "query-users" "view-realm")


CLIENT_REALM_MANAGEMENT="realm-management"

# KEYCLOAK_SERVER="${KEYCLOAK_URL:-http://localhost:8080/authkc}"
KEYCLOAK_SERVER="${KEYCLOAK_SERVER:-KEYCLOAK_URL}"
ADMIN_USER="${KEYCLOAK_ADMIN:-admin}"
ADMIN_PASSWORD="${KEYCLOAK_ADMIN_PASSWORD:-admin123}"

echo "🚀 Role assignment script started..."
echo "ℹ️ Server: $KEYCLOAK_SERVER"
echo "ℹ️ Waiting for Key<PERSON>loak to become ready..."

# Wait for Keycloak to fully start before attempting connection
echo "⏳ Initial wait for Keycloak startup (60 seconds)..."
sleep 60

# ⏳ Retry loop to wait for Keycloak connection with timeout
timeout=240
while ! /opt/keycloak/bin/kcadm.sh config credentials \
  --server "$KEYCLOAK_SERVER" \
  --realm master \
  --user "$ADMIN_USER" \
  --password "$ADMIN_PASSWORD" > /dev/null 2>&1; do

  if [ $timeout -le 0 ]; then
    echo "❌ Timeout waiting for Keycloak to be ready"
    echo "🔍 Attempting to check Keycloak health..."
    curl -s "$KEYCLOAK_SERVER/health" || echo "❌ Health check failed"
    echo "🔍 Attempting to check if Keycloak is responding..."
    curl -s "$KEYCLOAK_SERVER/" || echo "❌ Base URL check failed"
    exit 1
  fi
  echo "⏳ Waiting for Keycloak CLI to connect... ($timeout seconds remaining)"
  sleep 10
  timeout=$((timeout-10))
done

echo "✅ Connected to Keycloak via kcadm."

# --- Main Logic ---
echo "🔎 Getting ID for service account user '$SERVICE_USER' in realm '$REALM'..."
# Get the ID and remove potential quotes from the CSV output
SERVICE_ACCOUNT_ID=$(/opt/keycloak/bin/kcadm.sh get users -r "$REALM" -q username="$SERVICE_USER" --fields id --format csv --noquotes | tail -n1)

if [ -z "$SERVICE_ACCOUNT_ID" ]; then
  echo "❌ Service account user '$SERVICE_USER' not found in realm '$REALM'."
  echo "ℹ️ Please ensure the client '$CLIENT_ID' exists and has service accounts enabled."
  exit 1
fi

echo "✅ Found Service Account User ID: $SERVICE_ACCOUNT_ID"

for ROLE in "${ROLES[@]}"; do
  echo "🔐 Assigning role '$ROLE' from client '$CLIENT_REALM_MANAGEMENT'..."
  /opt/keycloak/bin/kcadm.sh add-roles \
    --uid "$SERVICE_ACCOUNT_ID" \
    --cclientid "$CLIENT_REALM_MANAGEMENT" \
    --rolename "$ROLE" \
    -r "$REALM"
done

echo "✅ Role assignment completed for service account of '$CLIENT_ID'."